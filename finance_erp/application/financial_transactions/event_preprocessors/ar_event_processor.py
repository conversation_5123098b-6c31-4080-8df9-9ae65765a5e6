import logging
from typing import Any, Dict

from finance_erp.application.financial_transactions.event_preprocessors.base_ar_event_processor import (
    BaseAREventProcessor,
)
from finance_erp.application.financial_transactions.event_preprocessors.base_bill_event_processor import (
    BaseBillEventProcessor,
)
from finance_erp.application.financial_transactions.transaction_engine import (
    TransactionEngine,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.repository.source_system_event_repository import (
    SourceSystemEventRepository,
)
from finance_erp.domain.financial_transactions.services.event_decomposition import (
    AREventDecomposer,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        AREventDecomposer,
        SourceSystemEventRepository,
        FinancialEventRepository,
        TransactionEngine,
    ]
)
class AREventProcessor(BaseAREventProcessor):
    """
    Base processor for AR (Accounts Receivable) events.
    Handles common AR event processing logic.
    """

    def __init__(
        self,
        decomposer: AREventDecomposer,
        source_event_repository: SourceSystemEventRepository,
        financial_event_repository: FinancialEventRepository,
        transaction_engine: TransactionEngine,
    ):
        super().__init__(
            decomposer, source_event_repository, financial_event_repository
        )
        self.transaction_engine = transaction_engine

    def get_source_system(self) -> str:
        return "AR"

    def _extract_hotel_id(self, event_payload: Dict[str, Any]) -> str:
        return event_payload.get("hotel_id")

    def _build_source_system_metadata(
        self, event_payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        return {
            "ar_transaction_id": event_payload.get("transaction_id"),
            "ar_reference_number": event_payload.get("reference_number"),
            "ar_event_type": event_payload.get("event_type"),
            "ar_amount": event_payload.get("amount"),
            "ar_currency": event_payload.get("currency", "INR"),
        }

    def process_ar_event_call(self, message_id: str, body: Dict[str, Any]):
        """Process AR event and generate transactions immediately."""
        try:
            # Extract basic AR event info
            ar_transaction_id = body.get("reference_number")
            hotel_id = self._extract_hotel_id(body)
            posting_date = body.get("date")

            if (not ar_transaction_id) and (not hotel_id):
                logger.error(
                    "Missing required fields in AR event: transaction_id=%s, hotel_id=%s",
                    ar_transaction_id,
                    hotel_id,
                )
                return

            logger.info(
                "Processing AR event: transaction_id=%s, hotel_id=%s",
                ar_transaction_id,
                hotel_id,
            )

            # Process through base processor
            financial_events = self.process_ar_event(message_id, body)

            # Trigger immediate transaction generation for AR financial events
            if financial_events:
                ar_event_ids = [
                    event.financial_event_id for event in financial_events
                ]
                self.transaction_engine.process(hotel_id, posting_date, ar_event_ids)

            logger.info(
                "Successfully processed AR event: transaction_id=%s",
                ar_transaction_id,
            )
            return financial_events

        except Exception as e:
            logger.exception(
                "Failed to process AR event: transaction_id=%s",
                body.get("transaction_id"),
            )
            raise


@register_instance(
    dependencies=[
        AREventDecomposer,
        SourceSystemEventRepository,
        FinancialEventRepository,
        TransactionEngine,
    ]
)
class ARCreditEventProcessor(AREventProcessor):
    """
    Processor for AR credit events.
    """

    def process_credit_event(self, message_id: str, body: Dict[str, Any]):
        """Process AR credit event."""
        logger.info(
            "Processing AR credit event: credit_id=%s",
            body.get("credit_id"),
        )
        return self.process_ar_event_call(message_id, body)


@register_instance(
    dependencies=[
        AREventDecomposer,
        SourceSystemEventRepository,
        FinancialEventRepository,
        TransactionEngine,
    ]
)
class ARDebitEventProcessor(AREventProcessor):
    """
    Processor for AR debit events.
    """

    def process_debit_event(self, message_id: str, event: Dict[str, Any]):
        """Process AR debit event."""
        event_payload = event.get("payload", {})
        event_payload["event_type"] = "ar_debit"

        logger.info(
            "Processing AR debit event: debit_id=%s",
            event_payload.get("debit_id"),
        )

        return self.process_ar_event_call(message_id, event)
