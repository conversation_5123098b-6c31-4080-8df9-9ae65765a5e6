import logging
from typing import Any, Dict

from finance_erp.application.financial_transactions.event_preprocessors.base_bill_event_processor import (
    BaseBillEventProcessor,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.repository.source_system_event_repository import (
    SourceSystemEventRepository,
)
from finance_erp.domain.financial_transactions.services.event_decomposition import (
    POSBillEventDecomposer,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        POSBillEventDecomposer,
        SourceSystemEventRepository,
        FinancialEventRepository,
    ]
)
class POSBillEventProcessor(BaseBillEventProcessor):
    def __init__(
        self,
        decomposer: POSBillEventDecomposer,
        source_event_repository: SourceSystemEventRepository,
        financial_event_repository: FinancialEventRepository,
    ):
        super().__init__(
            decomposer, source_event_repository, financial_event_repository
        )

    def get_source_system(self) -> str:
        return "pos"

    def _extract_hotel_id(self, event_payload: Dict[str, Any]) -> str:
        return event_payload.get("vendor_details", {}).get("hotel_id")

    def _build_source_system_metadata(
        self, event_payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        pos_seller_id = event_payload.get("vendor_details", {}).get("vendor_id")
        return {
            "bill_version": event_payload.get("version"),
            "app_id": event_payload.get("app_id"),
            "pos_seller_id": pos_seller_id,
            "vendor_details": event_payload.get("vendor_details"),
        }
