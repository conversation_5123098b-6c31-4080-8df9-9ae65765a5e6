import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Tuple

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    SourceSystemEventEntity,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.repository.source_system_event_repository import (
    SourceSystemEventRepository,
)
from finance_erp.domain.financial_transactions.services import AbstractEventDecomposer

logger = logging.getLogger(__name__)


class BaseAREventProcessor(ABC):
    """Abstract base class for ar event processors with common processing logic"""

    def __init__(
        self,
        decomposer: AbstractEventDecomposer,
        source_event_repository: SourceSystemEventRepository,
        financial_event_repository: FinancialEventRepository,
    ):
        self.decomposer = decomposer
        self.source_event_repository = source_event_repository
        self.financial_event_repository = financial_event_repository

    def process_ar_event(self, root_message_id: str, body: Dict[str, Any]):
        message_id = root_message_id
        try:
            message_id = self.get_build_message_id(root_message_id, body)

            reference_number = body.get("reference_number")
            hotel_id = self._extract_hotel_id(body)

            if not self._validate_event_prerequisites(body, message_id):
                return []

            # Create and store source event
            source_event = self._create_source_event_entity(message_id, body)

            # Decompose into financial events
            financial_events = self.decomposer.decompose_event(source_event)

            # Filter out already processed events
            events_to_upsert = self._filter_out_processed_events(financial_events)

            if events_to_upsert:
                self.financial_event_repository.bulk_upsert(events_to_upsert)

            # Update processing status
            source_event.mark_as_processed()
            self.source_event_repository.update(source_event)

            logger.info(
                "Successfully processed %s event: reference_number=%s, hotel_id=%s, financial_events=%d, events_upserted=%d",
                self.get_source_system(),
                reference_number,
                hotel_id,
                len(financial_events),
                len(events_to_upsert),
            )

            return events_to_upsert

        except Exception as e:
            logger.exception(
                "Failed to process %s reference_number event: reference_number=%s",
                self.get_source_system(),
                body.get("reference_number"),
            )
            self._store_failed_event(message_id, body, str(e))

    def get_build_message_id(
        self, root_message_id, event_payload: Dict[str, Any]
    ) -> str:
        entity_id, event_type = self._extract_event_id_and_type(event_payload)
        return f"{root_message_id}_{entity_id}_{event_type}"

    def _validate_event_prerequisites(self, event_payload, message_id):
        reference_number = event_payload.get("reference_number")
        hotel_id = self._extract_hotel_id(event_payload)
        if (not reference_number) and (not hotel_id):
            logger.error(
                "Missing required fields in %s ar event: reference_number=%s, hotel_id=%s",
                self.get_source_system(),
                reference_number,
                hotel_id,
            )
            return False

        # Check for duplicate processing
        if self._is_duplicate_event(message_id):
            logger.info(
                "Skipping duplicate %s ar event: message_id=%s, reference_number=%s",
                self.get_source_system(),
                message_id,
                reference_number,
            )
            return False

        if self._is_latest_event_processed(event_payload):
            logger.info(
                "Skipping processing of older version of %s ar event: reference_number=%s, version=%s",
                self.get_source_system(),
                reference_number,
                event_payload.get("version"),
            )
            return False

        return True

    def _is_duplicate_event(self, message_id: str) -> bool:
        existing_event = self.source_event_repository.find_by_message_id(message_id)
        return existing_event is not None

    def _filter_out_processed_events(self, financial_events):
        if not financial_events:
            return financial_events

        event_ids = [event.financial_event_id for event in financial_events]

        existing_events = self.financial_event_repository.find_by_financial_event_ids(
            event_ids
        )

        processed_event_ids = {
            event.financial_event_id
            for event in existing_events
            if event.processing_status == ProcessingStatus.PROCESSED
        }

        events_to_upsert = [
            event
            for event in financial_events
            if event.financial_event_id not in processed_event_ids
        ]

        return events_to_upsert

    def _is_latest_event_processed(self, event_payload: Dict[str, Any]) -> bool:
        entity_id, event_type = self._extract_event_id_and_type(event_payload)
        version_id = self._extract_version_id(event_payload)

        available_latest_version = (
            self.source_event_repository.get_max_version_for_entity(
                entity_id=event_type,
                source_system=self.get_source_system(),
                event_type=event_type,
            )
        )

        if not available_latest_version:
            return False

        if available_latest_version:
            return available_latest_version >= version_id

        return False

    @staticmethod
    def _extract_event_id_and_type(event_payload: Dict[str, Any]) -> Tuple[str, str]:
        entity_id = event_payload.get("reference_number") or "NOT_PRESENT"
        event_type = event_payload.get("event_type")
        return entity_id, event_type

    def _create_source_event_entity(
        self, message_id: str, event_payload: Dict[str, Any]
    ) -> SourceSystemEventEntity:
        hotel_id = self._extract_hotel_id(event_payload)
        version_id = self._extract_version_id(event_payload)

        entity_id, event_type = self._extract_event_id_and_type(event_payload)

        source_event = SourceSystemEventEntity(
            event_id=fin_erp_random_id_generator("SEV", max_length=20),
            message_id=message_id,
            source_system=self.get_source_system(),
            event_type=event_type,
            hotel_id=hotel_id,
            system_scope="hotel",
            entity_id=entity_id,
            event_payload=event_payload,
            received_at=datetime.now(),
            processing_status=ProcessingStatus.PROCESSING,
            source_system_metadata=self._build_source_system_metadata(event_payload),
            version_id=version_id,
        )
        return source_event

    def _store_failed_event(
        self, message_id: str, event_payload: Dict[str, Any], error: Any
    ):
        try:
            hotel_id = self._extract_hotel_id(event_payload) or "unknown"
            version_id = self._extract_version_id(event_payload)

            entity_id, event_type = self._extract_event_id_and_type(event_payload)

            failed_event = SourceSystemEventEntity(
                event_id=fin_erp_random_id_generator("SEV", max_length=20),
                message_id=message_id or "unknown",
                source_system=self.get_source_system(),
                event_type=event_type or "unknown",
                hotel_id=hotel_id,
                system_scope="hotel",
                entity_id=entity_id,
                event_payload=event_payload,
                received_at=datetime.now(),
                processing_status=ProcessingStatus.FAILED,
                failure_reason=str(error)
                if isinstance(error, str)
                else "; ".join(error),
                version_id=version_id,
            )
            self.source_event_repository.save(failed_event)

        except Exception as e:
            logger.exception(
                "Failed to store failed %s ar event: %s",
                self.get_source_system(),
                str(e),
            )

    @abstractmethod
    def get_source_system(self) -> str:
        """Return the source system name (e.g., 'crs', 'pos')"""
        pass

    @abstractmethod
    def _extract_hotel_id(self, event_payload: Dict[str, Any]) -> str:
        """Extract hotel ID from event payload (field name may vary by system)"""
        pass

    @abstractmethod
    def _build_source_system_metadata(
        self, event_payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Build system-specific metadata for source event"""
        pass

    @staticmethod
    def _extract_version_id(event_payload: Dict[str, Any]) -> int:
        return int(event_payload.get("version", 1))
