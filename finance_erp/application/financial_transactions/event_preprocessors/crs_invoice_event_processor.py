import logging
from typing import Any, Dict, List

from finance_erp.application.financial_transactions.event_preprocessors.base_bill_event_processor import (
    BaseBillEventProcessor,
)
from finance_erp.application.financial_transactions.transaction_engine import (
    TransactionEngine,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.repository.source_system_event_repository import (
    SourceSystemEventRepository,
)
from finance_erp.domain.financial_transactions.services.event_decomposition import (
    CRSInvoiceEventDecomposer,
)
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CRSInvoiceEventDecomposer,
        SourceSystemEventRepository,
        FinancialEventRepository,
        CrsServiceClient,
        TransactionEngine,
    ]
)
class CRSInvoiceEventProcessor(BaseBillEventProcessor):
    def __init__(
        self,
        decomposer: CRSInvoiceEventDecomposer,
        source_event_repository: SourceSystemEventRepository,
        financial_event_repository: FinancialEventRepository,
        crs_service_client: CrsServiceClient,
        transaction_engine: TransactionEngine,
    ):
        # Initialize with the stateless invoice decomposer
        super().__init__(
            decomposer, source_event_repository, financial_event_repository
        )
        self.crs_service_client = crs_service_client
        self.transaction_engine = transaction_engine

    def process_invoice_event(self, message_id: str, event: Dict[str, Any]):
        invoice_payload = event.get("payload", {})
        try:
            # Extract invoice and billed entity account info
            invoice_id = invoice_payload.get("invoice_id")
            invoice_number = invoice_payload.get("invoice_number")
            bill_id = invoice_payload.get("bill_id")
            billed_entity_account = invoice_payload.get("billed_entity_account", {})
            hotel_id = self._extract_hotel_id(invoice_payload)

            if not invoice_id or not bill_id or not billed_entity_account:
                logger.error(
                    "Missing required fields in invoice event: invoice_id=%s, bill_id=%s, billed_entity_account=%s",
                    invoice_id,
                    bill_id,
                    billed_entity_account,
                )
                return

            logger.info(
                "Processing CRS invoice event: invoice_id=%s, bill_id=%s, hotel_id=%s",
                invoice_id,
                bill_id,
                hotel_id,
            )

            # Fetch associated bill from CRS
            bill_data = self._fetch_bill_for_invoice(bill_id)

            # Create invoice context for metadata
            invoice_context = self._build_invoice_context(invoice_payload)

            # Enhance bill data with invoice-specific context
            enhanced_bill_data = self._enhance_bill_data_with_invoice_context(
                bill_data,
                billed_entity_account,
                invoice_id,
                invoice_number,
                invoice_context,
            )

            synthetic_event = {"entity_name": "invoice", "payload": enhanced_bill_data}

            # Process through base processor with stateless decomposer
            financial_events = self.process_bill_event(message_id, synthetic_event)

            # Trigger immediate transaction generation for invoice financial events
            if financial_events:
                invoice_event_ids = [
                    event.financial_event_id for event in financial_events
                ]
                self.transaction_engine.process(
                    hotel_id, invoice_payload.get("posting_date"), invoice_event_ids
                )

            logger.info(
                "Successfully processed CRS invoice event: invoice_id=%s, bill_id=%s",
                invoice_id,
                bill_id,
            )

        except Exception as e:
            logger.exception(
                "Failed to process CRS invoice event: invoice_id=%s, bill_id=%s",
                invoice_payload.get("invoice_id"),
                invoice_payload.get("bill_id"),
            )
            raise

    def _fetch_bill_for_invoice(self, bill_id: str) -> Dict[str, Any]:
        logger.info("Fetching bill %s from CRS for invoice processing", bill_id)
        return self.crs_service_client.get_bill(bill_id)

    @staticmethod
    def _build_invoice_context(invoice_payload: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "issued_to_type": invoice_payload.get("issued_to_type"),
            "issued_by_type": invoice_payload.get("issued_by_type"),
            "bill_to": invoice_payload.get("bill_to"),
            "issued_by": invoice_payload.get("issued_by"),
            "invoice_date": invoice_payload.get("invoice_date"),
            "invoice_due_date": invoice_payload.get("invoice_due_date"),
            "status": invoice_payload.get("status"),
        }

    @staticmethod
    def _enhance_bill_data_with_invoice_context(
        bill_data: Dict[str, Any],
        billed_entity_account: Dict[str, Any],
        invoice_id: str,
        invoice_number: str,
        invoice_context: Dict[str, Any],
    ) -> Dict[str, Any]:
        # Add invoice-specific context to bill data for the decomposer
        enhanced_bill_data = bill_data.copy()
        enhanced_bill_data["_invoice_context"] = {
            "target_billed_entity_account": billed_entity_account,
            "invoice_id": invoice_id,
            "invoice_number": invoice_number,
            "invoice_context": invoice_context,
        }
        enhanced_bill_data["event_type"] = "crs_invoice"
        return enhanced_bill_data

    def get_source_system(self) -> str:
        return "crs"

    def _extract_hotel_id(self, event_payload: Dict[str, Any]) -> str:
        # For invoice events, hotel_id might be in vendor_details
        vendor_details = event_payload.get("vendor_details", {})
        return vendor_details.get("hotel_id") or event_payload.get("hotel_id")

    def _build_source_system_metadata(
        self, event_payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        return {
            "invoice_version": event_payload.get("version"),
            "invoice_status": event_payload.get("status"),
            "parent_reference_number": event_payload.get("parent_info", {}).get(
                "booking_reference_number"
            ),
        }
