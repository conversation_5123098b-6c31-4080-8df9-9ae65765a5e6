from typing import Any, Dict, List

from pydantic import BaseModel
from ths_common.constants.billing_constants import ChargeStatus, PaymentStatus

from finance_erp.domain.financial_transactions.constants import FinancialEventTypes
from finance_erp.domain.financial_transactions.services.financial_event_id_generator import (
    FinancialEventIdGenerator,
)


class EntityTypeConfig(BaseModel):
    event_type: str
    id_field: str
    additional_fields: List[str]
    posting_state: str

    def generate_financial_event_id(self, item: Dict[str, Any]) -> str:
        bill_id = item.get("bill_id")
        entity_id = item.get(self.id_field)

        if self.event_type == FinancialEventTypes.CRS_PAYMENT:
            return FinancialEventIdGenerator.generate_id(
                self.event_type, bill_id=bill_id, payment_id=entity_id
            )
        elif self.event_type == FinancialEventTypes.CRS_CHARGE:
            return FinancialEventIdGenerator.generate_id(
                self.event_type, bill_id=bill_id, charge_id=entity_id
            )
        elif self.event_type == FinancialEventTypes.CRS_ALLOWANCE:
            return FinancialEventIdGenerator.generate_id(
                self.event_type,
                bill_id=bill_id,
                charge_id=item.get("charge_id"),
                charge_split_id=item.get("charge_split_id"),
                allowance_id=entity_id,
            )
        elif self.event_type == FinancialEventTypes.CRS_INVOICED_CHARGE:
            return FinancialEventIdGenerator.generate_id(
                self.event_type,
                bill_id=bill_id,
                charge_id=item.get("charge_id"),
                charge_split_id=entity_id,
                invoice_id=item.get("invoice_id"),
            )
        elif self.event_type == FinancialEventTypes.CRS_INVOICED_PAYMENT:
            return FinancialEventIdGenerator.generate_id(
                self.event_type,
                bill_id=bill_id,
                payment_id=item.get("payment_id"),
                payment_split_id=entity_id,
                invoice_id=item.get("invoice_id"),
            )
        elif self.event_type == FinancialEventTypes.CRS_INVOICED_ALLOWANCE:
            return FinancialEventIdGenerator.generate_id(
                self.event_type,
                bill_id=bill_id,
                charge_id=item.get("charge_id"),
                charge_split_id=item.get("charge_split_id"),
                allowance_id=entity_id,
                invoice_id=item.get("invoice_id"),
            )
        else:
            raise ValueError(f"Unknown event type: {self.event_type}")


ENTITY_CONFIGS = {
    FinancialEventTypes.CRS_PAYMENT: EntityTypeConfig(
        event_type="crs_payment",
        id_field="payment_id",
        additional_fields=[],
        posting_state=PaymentStatus.POSTED.value,
    ),
    FinancialEventTypes.CRS_CHARGE: EntityTypeConfig(
        event_type="crs_charge",
        id_field="charge_id",
        additional_fields=[],
        posting_state=ChargeStatus.CONSUMED.value,
    ),
    FinancialEventTypes.CRS_ALLOWANCE: EntityTypeConfig(
        event_type="crs_allowance",
        id_field="allowance_id",
        additional_fields=["charge_id", "charge_split_id"],
        posting_state=ChargeStatus.CONSUMED.value,
    ),
    FinancialEventTypes.CRS_INVOICED_CHARGE: EntityTypeConfig(
        event_type="crs_invoiced_charge",
        id_field="charge_split_id",
        additional_fields=["charge_id", "invoice_id"],
        posting_state=ChargeStatus.CONSUMED.value,
    ),
    FinancialEventTypes.CRS_INVOICED_PAYMENT: EntityTypeConfig(
        event_type="crs_invoiced_payment",
        id_field="payment_split_id",
        additional_fields=["payment_id", "invoice_id"],
        posting_state=PaymentStatus.POSTED.value,
    ),
    FinancialEventTypes.CRS_INVOICED_ALLOWANCE: EntityTypeConfig(
        event_type="crs_invoiced_allowance",
        id_field="allowance_id",
        additional_fields=["charge_id", "charge_split_id", "invoice_id"],
        posting_state=ChargeStatus.CONSUMED.value,
    ),
}


class PostedItemsSummary(BaseModel):
    payments: List[Dict[str, Any]]
    charges: List[Dict[str, Any]]
    allowances: List[Dict[str, Any]]
