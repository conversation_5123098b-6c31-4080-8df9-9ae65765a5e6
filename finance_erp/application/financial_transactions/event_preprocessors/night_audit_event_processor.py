import logging
from datetime import date, datetime
from typing import Any, Dict, List, Set

from ths_common.utils.collectionutils import chunks

from finance_erp.application.financial_transactions.event_preprocessors.crs_bill_event_processor import (
    CRSBillEventProcessor,
)
from finance_erp.application.financial_transactions.event_preprocessors.utils.config import (
    ENTITY_CONFIGS,
    PostedItemsSummary,
)
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.financial_transactions.constants import (
    FinancialEventTypes,
    ProcessingStatus,
    SourceSystemStatus,
)
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    SourceSystemEventEntity,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.repository.source_system_event_repository import (
    SourceSystemEventRepository,
)
from finance_erp.infrastructure.external_clients.crs_service_client import (
    CrsServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        SourceSystemEventRepository,
        FinancialEventRepository,
        CRSBillEventProcessor,
        CrsServiceClient,
        JobSchedulerService,
    ]
)
class NightAuditEventProcessor:
    def __init__(
        self,
        source_event_repository: SourceSystemEventRepository,
        financial_event_repository: FinancialEventRepository,
        crs_bill_processor: CRSBillEventProcessor,
        crs_service_client: CrsServiceClient,
        job_scheduler_service: JobSchedulerService,
    ):
        self.source_event_repository = source_event_repository
        self.financial_event_repository = financial_event_repository
        self.crs_bill_processor = crs_bill_processor
        self.crs_service_client = crs_service_client
        self.job_scheduler_service = job_scheduler_service

    def process_night_audit_event(self, message_id: str, event: Dict[str, Any]):
        event_payload = event.get("payload", {})

        try:
            night_audit_id = event_payload.get("night_audit_id")
            hotel_id = event_payload.get("hotel_id")
            business_date = event_payload.get("business_date")
            summary = event_payload.get("summary", {})

            if not self._validate_event_prerequisites(message_id):
                return

            # Create and store source event
            source_event = self._create_source_event_entity(message_id, event_payload)

            # Process the night audit summary
            self._process_night_audit_summary(
                hotel_id=hotel_id,
                business_date=business_date,
                summary=summary,
                source_event_id=source_event.event_id,
            )

            # Schedule daily event processing job
            self.job_scheduler_service.schedule_day_end_transaction_generation_job(
                hotel_id=hotel_id,
                processing_date=business_date,
            )

            # Update processing status
            source_event.mark_as_processed()
            self.source_event_repository.update(source_event)

            logger.info(
                "Successfully processed night audit event: night_audit_id=%s, hotel_id=%s, business_date=%s",
                night_audit_id,
                hotel_id,
                business_date,
            )

        except Exception as e:
            logger.exception(
                "Failed to process night audit event: night_audit_id=%s",
                event_payload.get("night_audit_id"),
            )
            self._store_failed_event(message_id, event_payload, str(e))

    def _validate_event_prerequisites(
        self,
        message_id: str,
    ) -> bool:
        # Check if we've already processed this message
        existing_event = self.source_event_repository.find_by_message_id(message_id)
        if existing_event:
            logger.info(
                "Night audit event already processed for message_id: %s", message_id
            )
            return False

        return True

    def _create_source_event_entity(
        self, message_id: str, event_payload: Dict[str, Any]
    ) -> SourceSystemEventEntity:
        night_audit_id = event_payload.get("night_audit_id")
        hotel_id = event_payload.get("hotel_id")
        version_id = self._extract_version_id(event_payload)

        source_event = SourceSystemEventEntity(
            event_id=fin_erp_random_id_generator("SEV", max_length=20),
            message_id=message_id,
            source_system="crs",
            event_type="night_audit",
            hotel_id=hotel_id,
            system_scope="hotel",
            entity_id=night_audit_id,
            event_payload=event_payload,
            received_at=datetime.now(),
            processing_status=ProcessingStatus.PROCESSING,
            source_system_metadata=self._build_source_system_metadata(event_payload),
            version_id=version_id,
        )
        return source_event

    @staticmethod
    def _build_source_system_metadata(event_payload: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "business_date": event_payload.get("business_date"),
            "scheduled_time": event_payload.get("scheduled_time"),
            "status": event_payload.get("status"),
            "failure_message": event_payload.get("failure_message"),
        }

    @staticmethod
    def _extract_version_id(event_payload: Dict[str, Any]) -> int:
        return int(event_payload.get("version", 1))

    def _store_failed_event(
        self, message_id: str, event_payload: Dict[str, Any], error: Any
    ):
        try:
            night_audit_id = event_payload.get("night_audit_id", "unknown")
            hotel_id = event_payload.get("hotel_id", "unknown")
            version_id = self._extract_version_id(event_payload)

            failed_event = SourceSystemEventEntity(
                event_id=fin_erp_random_id_generator("SEV", max_length=20),
                message_id=message_id,
                source_system="crs",
                event_type="night_audit",
                hotel_id=hotel_id,
                system_scope="hotel",
                entity_id=night_audit_id,
                event_payload=event_payload,
                received_at=datetime.now(),
                processing_status=ProcessingStatus.FAILED,
                failure_reason=str(error)
                if isinstance(error, str)
                else "; ".join(error),
                version_id=version_id,
            )
            self.source_event_repository.save(failed_event)

        except Exception as e:
            logger.exception("Failed to store failed night audit event: %s", str(e))

    def _process_night_audit_summary(
        self,
        hotel_id: str,
        business_date: str,
        summary: Dict[str, Any],
        source_event_id: str,
    ):
        business_date_obj = datetime.strptime(business_date, "%Y-%m-%d").date()

        # Extract and structure posted items
        posted_items = self._extract_posted_items_summary(summary)

        logger.info(
            "Processing night audit summary for hotel %s, date %s: %d payments, %d charges, %d allowances",
            hotel_id,
            business_date,
            len(posted_items.payments),
            len(posted_items.charges),
            len(posted_items.allowances),
        )

        self._process_missing_bills_workflow(hotel_id, posted_items, source_event_id)

        self._update_posting_statuses_workflow(
            hotel_id,
            business_date_obj,
            posted_items,
        )

    @staticmethod
    def _extract_posted_items_summary(summary: Dict[str, Any]) -> PostedItemsSummary:
        return PostedItemsSummary(
            payments=summary.get("payments_posted", []),
            charges=summary.get("charges_posted", []),
            allowances=summary.get("allowances_posted", []),
        )

    def _process_missing_bills_workflow(
        self,
        hotel_id: str,
        posted_items: PostedItemsSummary,
        source_event_id: str,
    ):
        # Collect all missing bills for batch processing
        missing_bills = set()

        # Process each type of posted item and collect missing bills
        missing_bills.update(
            self._verify_and_collect_missing_items(
                hotel_id,
                posted_items.payments,
                FinancialEventTypes.CRS_PAYMENT,
            )
        )
        missing_bills.update(
            self._verify_and_collect_missing_items(
                hotel_id,
                posted_items.charges,
                FinancialEventTypes.CRS_CHARGE,
            )
        )
        missing_bills.update(
            self._verify_and_collect_missing_items(
                hotel_id,
                posted_items.allowances,
                FinancialEventTypes.CRS_ALLOWANCE,
            )
        )

        # Batch fetch and process missing bills
        if missing_bills:
            self._batch_fetch_and_process_missing_bills(missing_bills, source_event_id)

    def _update_posting_statuses_workflow(
        self,
        hotel_id: str,
        business_date: date,
        posted_items: PostedItemsSummary,
    ):
        self._batch_update_posting_status(
            hotel_id,
            business_date,
            posted_items.payments,
            FinancialEventTypes.CRS_PAYMENT,
        )
        self._batch_update_posting_status(
            hotel_id,
            business_date,
            posted_items.charges,
            FinancialEventTypes.CRS_CHARGE,
        )
        self._batch_update_posting_status(
            hotel_id,
            business_date,
            posted_items.allowances,
            FinancialEventTypes.CRS_ALLOWANCE,
        )

    def _verify_and_collect_missing_items(
        self,
        hotel_id: str,
        posted_items: List[Dict[str, Any]],
        entity_type: str,
    ) -> Set[str]:
        if not posted_items:
            return set()

        config = ENTITY_CONFIGS[entity_type]
        missing_bills = set()

        expected_event_ids = []
        item_lookup = {}

        for item in posted_items:
            bill_id = item.get("bill_id")

            financial_event_id = config.generate_financial_event_id(item)
            expected_event_ids.append(financial_event_id)
            item_lookup[financial_event_id] = bill_id

        if not expected_event_ids:
            return missing_bills

        existence_map = self.financial_event_repository.check_events_existence(
            expected_event_ids
        )

        for financial_event_id, exists in existence_map.items():
            if not exists:
                bill_id = item_lookup[financial_event_id]
                missing_bills.add(bill_id)

        return missing_bills

    def _batch_fetch_and_process_missing_bills(
        self,
        missing_bills: Set[str],
        source_event_id: str,
    ):
        if not missing_bills:
            return

        logger.info("Batch fetching %d missing bills from CRS", len(missing_bills))

        missing_bills_list = list(missing_bills)
        total_processed = 0
        total_failed = 0

        for bill_batch in chunks(missing_bills_list, 10):
            bill_ids_str = ",".join(bill_batch)
            bills_data = self.crs_service_client.get_bills(bill_ids=bill_ids_str)

            batch_processed = 0
            for bill_data in bills_data:
                bill_id = bill_data.get("bill_id")
                synthetic_message_id = f"night_audit_{source_event_id}_{bill_id}"
                synthetic_event = {"entity_name": "bill", "payload": bill_data}

                self.crs_bill_processor.process_bill_event(
                    synthetic_message_id,
                    synthetic_event,
                )
                batch_processed += 1

            total_processed += batch_processed

        logger.info(
            "Completed missing bills processing: %d processed, %d failed out of %d total",
            total_processed,
            total_failed,
            len(missing_bills_list),
        )

    def _batch_update_posting_status(
        self,
        hotel_id: str,
        business_date: date,
        posted_items: List[Dict[str, Any]],
        entity_type: str,
    ):
        if not posted_items:
            return

        config = ENTITY_CONFIGS[entity_type]
        expected_event_ids = []

        for item in posted_items:
            financial_event_id = config.generate_financial_event_id(item)
            expected_event_ids.append(financial_event_id)

        if not expected_event_ids:
            return

        existing_events = self.financial_event_repository.find_by_financial_event_ids(
            expected_event_ids
        )

        events_to_update = []
        for event in existing_events:
            if event.processing_status == ProcessingStatus.PENDING:
                event.event_data["status"] = config.posting_state
                event.source_system_status = SourceSystemStatus.POSTED
                event.posting_date = business_date
                events_to_update.append(event)

        if events_to_update:
            self.financial_event_repository.update_many(events_to_update)
            logger.info(
                "Batch updated posting status for %d %s events",
                len(events_to_update),
                entity_type,
            )
