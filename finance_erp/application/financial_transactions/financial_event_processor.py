import logging
from datetime import date
from typing import List

from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.services.partial_failure_tracker import (
    PartialFailureTracker,
)
from finance_erp.domain.financial_transactions.services.transaction_generation import (
    TransactionGenerator,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        TransactionGenerator,
        FinancialEventRepository,
    ]
)
class FinancialEventProcessor:
    def __init__(
        self,
        transaction_generator: TransactionGenerator,
        financial_event_repository: FinancialEventRepository,
    ):
        self.transaction_generator = transaction_generator
        self.financial_event_repository = financial_event_repository

    def process_financial_events_to_transactions(
        self,
        financial_events: List[FinancialEventEntity],
        config_master: ConfigMaster,
        processing_date: date,
    ) -> TransactionProcessingResult:
        try:
            result: TransactionProcessingResult = (
                self.transaction_generator.generate_transactions_from_events(
                    financial_events,
                    config_master,
                )
            )

            # Handle partial failures and update event statuses
            summary = PartialFailureTracker.process_and_update_events(
                financial_events,
                result,
                str(processing_date),
            )

            # Bulk update events
            self.financial_event_repository.update_many(financial_events)

            logger.info(
                "Processed %d financial events: %d successful, %d failed, %d partial"
                " failures, %d transactions, %d errors",
                summary["total_events"],
                summary["successful_events"],
                summary["failed_events"],
                summary["partial_failure_events"],
                summary["total_transactions"],
                summary["total_errors"],
            )

            return result

        except Exception as e:
            logger.exception(
                "Failed to process financial events to transactions: %s", str(e)
            )
            # Mark events as failed
            for event in financial_events:
                event.mark_as_failed(str(e))
            self.financial_event_repository.update_many(financial_events)
            raise
