import logging
from datetime import date
from typing import List, Optional

from finance_erp.application.back_office.comand_handlers.update_transaction_master import (
    UpdateTransactionMasterCommandHandler,
)
from finance_erp.application.financial_transactions.financial_event_processor import (
    FinancialEventProcessor,
)
from finance_erp.application.financial_transactions.transaction_aggregator import (
    TransactionAggregator,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job.job_constants import Job<PERSON><PERSON>
from finance_erp.async_job.job_registry import JobRegistry
from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.back_office.repository.transaction_master import (
    TransactionMasterRepository,
)
from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)
from finance_erp.domain.financial_transactions.repository.aggregated_transaction_repository import (
    AggregatedTransactionRepository,
)
from finance_erp.domain.financial_transactions.repository.financial_event_repository import (
    FinancialEventRepository,
)
from finance_erp.domain.financial_transactions.repository.transaction_repository import (
    TransactionRepository,
)
from finance_erp.domain.financial_transactions.services.external_pos_processor import (
    ExternalPosItemProcessor,
)
from finance_erp.domain.financial_transactions.services.reseller_event_generator import (
    ResellerEventGenerator,
)
from finance_erp.domain.pos.repository.pos_revenue_repository import (
    POSRevenueRepository,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        FinancialEventRepository,
        FinancialEventProcessor,
        UpdateTransactionMasterCommandHandler,
        TenantSettings,
        TransactionMasterRepository,
        TransactionRepository,
        TransactionAggregator,
        ResellerEventGenerator,
        ExternalPosItemProcessor,
        POSRevenueRepository,
        AggregatedTransactionRepository,
        JobRegistry,
    ]
)
class TransactionEngine:
    def __init__(
        self,
        financial_event_repository: FinancialEventRepository,
        financial_event_processor: FinancialEventProcessor,
        transaction_master_updater: UpdateTransactionMasterCommandHandler,
        tenant_settings: TenantSettings,
        transaction_master_repository: TransactionMasterRepository,
        transaction_repository: TransactionRepository,
        transaction_aggregator: TransactionAggregator,
        reseller_event_generator: ResellerEventGenerator,
        external_pos_item_processor: ExternalPosItemProcessor,
        pos_revenue_repository: POSRevenueRepository,
        aggregated_transaction_repository: AggregatedTransactionRepository,
        job_registry: JobRegistry,
    ):
        self.financial_event_repository = financial_event_repository
        self.financial_event_processor = financial_event_processor
        self.transaction_master_updater = transaction_master_updater
        self.tenant_settings = tenant_settings
        self.transaction_master_repository = transaction_master_repository
        self.transaction_repository = transaction_repository
        self.transaction_aggregator = transaction_aggregator
        self.reseller_event_generator = reseller_event_generator
        self.external_pos_item_processor = external_pos_item_processor
        self.pos_revenue_repository = pos_revenue_repository
        self.aggregated_transaction_repository = aggregated_transaction_repository

        job_registry.register(JobName.FINANCIAL_TXN_GENERATOR_JOB, self.process)

    def process(
        self,
        hotel_id: str,
        processing_date: date,
        financial_event_ids: Optional[List[str]] = None,
    ):

        if financial_event_ids:
            logger.info(
                "Starting financial transaction processing for hotel %s, date %s, specific events: %d",
                hotel_id,
                processing_date,
                len(financial_event_ids),
            )
        else:
            logger.info(
                "Starting financial transaction processing for hotel %s, date %s",
                hotel_id,
                processing_date,
            )

        try:
            # Step 1: Load pending financial events
            financial_events = self._load_pending_financial_events(
                hotel_id, processing_date, financial_event_ids
            )
            if not financial_events:
                return self._create_empty_result()

            # Step 2: Setup configuration
            config_master = self._build_config_master(hotel_id)

            # Step 3: Generate buy-side events (currently disabled)
            buy_side_events = self._generate_buy_side_events(financial_events)
            all_events = financial_events + buy_side_events

            # Step 4: Process events to transactions
            (transactions, processing_errors,) = self._process_events_to_transactions(
                all_events, config_master, processing_date
            )

            # Step 5: Aggregate and  transactions
            aggregated_transactions = self._aggregate_and_persist_transactions(
                transactions, hotel_id
            )

            # Step 6: Return processing summary
            return self._create_processing_result(
                financial_events,
                buy_side_events,
                all_events,
                transactions,
                aggregated_transactions,
                processing_errors,
            )

        except Exception as e:
            logger.exception(
                "Daily event processing failed for hotel %s, date %s: %s",
                hotel_id,
                processing_date,
                str(e),
            )
            raise

    def _load_pending_financial_events(
        self,
        hotel_id: str,
        processing_date: date,
        financial_event_ids: Optional[List[str]] = None,
    ):
        if financial_event_ids:
            # Process specific financial events by IDs
            financial_events = (
                self.financial_event_repository.find_by_financial_event_ids(
                    financial_event_ids
                )
            )

            # Filter to only include pending events for the specified hotel and date
            filtered_events = [
                event
                for event in financial_events
                if (
                    event.hotel_id == hotel_id
                    and processing_date is None
                    or event.posting_date == processing_date
                    and event.processing_status == ProcessingStatus.PENDING
                )
            ]

            if not filtered_events:
                logger.info(
                    "No pending financial events found for hotel %s, date %s, specified event IDs: %d",
                    hotel_id,
                    processing_date,
                    len(financial_event_ids),
                )
                return []

            logger.info(
                "Found %d pending financial events for processing from %d specified IDs",
                len(filtered_events),
                len(financial_event_ids),
            )
            return filtered_events
        else:
            # Process all pending events for the date (existing behavior)
            financial_events = self.financial_event_repository.find_pending_events(
                hotel_id=hotel_id, posting_date=processing_date
            )

            if not financial_events:
                logger.info(
                    "No pending financial events found for hotel %s, date %s",
                    hotel_id,
                    processing_date,
                )
                return []

            logger.info(
                "Found %d pending financial events for processing",
                len(financial_events),
            )
            return financial_events

    @staticmethod
    def _create_empty_result():
        return {
            "sell_events_processed": 0,
            "buy_events_generated": 0,
            "total_events_processed": 0,
            "transactions_created": 0,
            "aggregated_transactions_created": 0,
            "processing_errors": 0,
            "financial_events_errors": 0,
            "prestored_data_errors": 0,
            "processing_completed": False,
        }

    def _build_config_master(self, hotel_id: str) -> ConfigMaster:
        txn_master_codes = self.transaction_master_repository.load_all(
            hotel_id,
            is_active=True,
        )

        return ConfigMaster(
            hotel_id=hotel_id,
            account_master=None,
            txn_master_codes=txn_master_codes,
            receive_later_payment_modes=None,
            credit_payment_modes=None,
            refund_modes_to_exclude=None,
            payment_config=None,
        )

    def _generate_buy_side_events(self, financial_events):
        logger.info("Generating buy-side events for eligible sell-side events")

        # TODO: Proper integration of buy side data (PROM-18473)
        # buy_side_events = self.reseller_event_generator.generate_buy_side_events(financial_events)
        buy_side_events = []

        logger.info("Generated %d buy-side events", len(buy_side_events))
        return buy_side_events

    def _process_events_to_transactions(
        self,
        all_events,
        config_master,
        processing_date,
    ):
        logger.info("Processing %d events to transactions", len(all_events))

        results = (
            self.financial_event_processor.process_financial_events_to_transactions(
                all_events,
                config_master,
                processing_date,
            )
        )

        prestored_data_result = self._process_prestored_data(
            config_master.hotel_id, processing_date, config_master
        )

        results.extend(prestored_data_result)

        if results.has_errors > 0:
            logger.warning("Processing generated %d errors", results.error_count)

        return results.transactions, results.error_count

    def _aggregate_and_persist_transactions(self, transactions, hotel_id):

        if not transactions:
            logger.info("No transactions to aggregate")
            return []

        logger.info("Aggregating %d transactions", len(transactions))

        aggregated_transactions = []

        # For now we are not aggregating transactions here, aggregation logic need more definition from Product
        # aggregated_transactions = self.transaction_aggregator.aggregate_transactions(
        #     transactions,
        #     hotel_id,
        # )

        logger.info("Updating %d transactions status in database", len(transactions))
        self.transaction_repository.insert_many(transactions)
        self.aggregated_transaction_repository.insert_many(aggregated_transactions)

        return aggregated_transactions

    @staticmethod
    def _create_processing_result(
        financial_events,
        buy_side_events,
        all_events,
        transactions,
        aggregated_transactions,
        total_errors,
    ):

        # Log completion summary
        logger.info(
            "Daily event processing completed: "
            "sell_events=%d, buy_events=%d, total_events=%d, transactions=%d, aggregated_transactions=%d, errors=%d",
            len(financial_events),
            len(buy_side_events),
            len(all_events),
            len(transactions),
            len(aggregated_transactions),
            total_errors,
        )

        return {
            "sell_events_processed": len(financial_events),
            "buy_events_generated": len(buy_side_events),
            "total_events_processed": len(all_events),
            "transactions_created": len(transactions),
            "aggregated_transactions_created": len(aggregated_transactions),
            "processing_errors": total_errors,
            "processing_completed": True,
        }

    def _process_prestored_data(
        self, hotel_id: str, processing_date: date, config_master
    ):

        result = TransactionProcessingResult.empty()

        # Process External POS data (e.g., Touche POS Settle to Table data)
        pos_items = self.pos_revenue_repository.fetch(hotel_id, processing_date)
        if pos_items:
            pos_result = self.external_pos_item_processor.process(
                pos_items, config_master
            )
            result.extend_with(pos_result)

            logger.info(
                "Processed %d POS items: %d transactions, %d errors",
                len(pos_items),
                pos_result.transaction_count,
                pos_result.error_count,
            )

        # Add more processors here as needed
        # e.g., other_result = self.other_processor.process(...)
        # result.extend_with(other_result)

        return result
