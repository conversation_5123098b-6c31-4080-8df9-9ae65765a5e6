import logging
from datetime import date
from typing import List

from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    AggregatedTransactionEntity,
    TransactionEntity,
)
from finance_erp.domain.financial_transactions.repository.transaction_repository import (
    TransactionRepository,
)
from finance_erp.domain.financial_transactions.services.transaction_aggregation import (
    TransactionAggregationService,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        TransactionRepository,
    ]
)
class TransactionAggregator:
    def __init__(
        self,
        transaction_repository: TransactionRepository,
    ):
        self.transaction_repository = transaction_repository

    def aggregate_transactions_for_date(
        self, hotel_id: str, processing_date: date
    ) -> List[AggregatedTransactionEntity]:

        logger.info(
            "Starting transaction aggregation for hotel %s, date %s",
            hotel_id,
            processing_date,
        )

        pending_transactions = self.transaction_repository.find_pending_for_aggregation(
            hotel_id, processing_date
        )

        if not pending_transactions:
            logger.info("No pending transactions found for aggregation")
            return []

        return self.aggregate_transactions(
            pending_transactions,
            hotel_id,
        )

    def aggregate_transactions(
        self,
        transactions: List[TransactionEntity],
        hotel_id: str,
    ) -> List[AggregatedTransactionEntity]:

        if not transactions:
            logger.info("No transactions provided for aggregation")
            return []

        logger.info(
            "Starting transaction aggregation for %d transactions",
            len(transactions),
        )

        aggregated_transactions = TransactionAggregationService.aggregate_transactions(
            transactions,
            hotel_id,
        )

        if aggregated_transactions:
            self._mark_transactions_as_processed(transactions, aggregated_transactions)

        logger.info(
            "Aggregated %d transactions into %d aggregated transactions",
            len(transactions),
            len(aggregated_transactions),
        )

        return aggregated_transactions

    @staticmethod
    def _mark_transactions_as_processed(
        transactions: List[TransactionEntity],
        aggregated_transactions: List[AggregatedTransactionEntity],
    ) -> None:
        txn_to_aggregated_map = {}
        for aggregated_txn in aggregated_transactions:
            txn_ids = aggregated_txn.metadata.get("transaction_ids", [])
            for txn_id in txn_ids:
                txn_to_aggregated_map[txn_id] = aggregated_txn.aggregated_transaction_id

        for txn in transactions:
            if txn.transaction_id in txn_to_aggregated_map:
                txn.mark_as_processed(txn_to_aggregated_map[txn.transaction_id])
