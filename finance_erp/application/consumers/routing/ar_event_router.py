import logging
from typing import Any, Dict

from flask import current_app

from finance_erp.common.decorators import session_manager
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        # Will add AR event processors when implemented
    ]
)
class AREventRouter:
    """
    Router for handling AR (Accounts Receivable) events.
    Processes credit events, debit events, and reversals.
    """

    # Entity names for AR events
    CREDIT_ENTITY_NAME = "credit"
    DEBIT_ENTITY_NAME = "debit"
    REVERSAL_ENTITY_NAME = "reversal"

    def __init__(
        self,
        # ar_credit_event_processor,  # Will be ARCreditEventProcessor when implemented
        # ar_debit_event_processor,   # Will be ARDebitEventProcessor when implemented
    ):
        # self.ar_credit_event_processor = ar_credit_event_processor
        # self.ar_debit_event_processor = ar_debit_event_processor
        pass

    def can_handle_message(self, body: Dict[str, Any]) -> bool:
        """Check if the message contains AR events we can handle."""
        events = body.get("events", [])
        return any(
            event.get("entity_name")
            in (
                self.CREDIT_ENTITY_NAME,
                self.DEBIT_ENTITY_NAME,
                self.REVERSAL_ENTITY_NAME,
            )
            for event in events
        )

    def process_credit_events(self, body: Dict[str, Any]) -> None:
        """Process AR credit events."""
        if not self.can_handle_message(body):
            logger.debug("No relevant AR credit events found in message")
            return

        logger.debug("Processing AR credit events")
        message_id = body.get("message_id")

        with current_app.test_request_context():
            try:
                for event in body.get("events", []):
                    entity_name = event.get("entity_name")

                    if entity_name == self.CREDIT_ENTITY_NAME:
                        self._process_credit_event(message_id, event)
                    elif entity_name == self.REVERSAL_ENTITY_NAME:
                        self._process_reversal_event(message_id, event)

            except Exception as e:
                logger.exception(
                    "AR credit event router error while processing message: %s", e
                )

    def process_debit_events(self, body: Dict[str, Any]) -> None:
        """Process AR debit events."""
        if not self.can_handle_message(body):
            logger.debug("No relevant AR debit events found in message")
            return

        logger.debug("Processing AR debit events")
        message_id = body.get("message_id")

        with current_app.test_request_context():
            try:
                for event in body.get("events", []):
                    entity_name = event.get("entity_name")

                    if entity_name == self.DEBIT_ENTITY_NAME:
                        self._process_debit_event(message_id, event)
                    elif entity_name == self.REVERSAL_ENTITY_NAME:
                        self._process_reversal_event(message_id, event)

            except Exception as e:
                logger.exception(
                    "AR debit event router error while processing message: %s", e
                )

    @session_manager(commit=True)
    def _process_credit_event(self, message_id: str, event: Dict[str, Any]) -> None:
        """Process individual AR credit event."""
        event_payload = event.get("payload", {})
        credit_id = event_payload.get("credit_id")

        logger.info("Processing AR credit event: %s", credit_id)

        try:
            # TODO: Implement AR credit event processing
            # self.ar_credit_event_processor.process_credit_event(message_id, event)
            logger.info("AR credit event processing not yet implemented: %s", credit_id)
        except Exception as e:
            logger.exception("Failed to process AR credit event %s: %s", credit_id, str(e))
            raise

    @session_manager(commit=True)
    def _process_debit_event(self, message_id: str, event: Dict[str, Any]) -> None:
        """Process individual AR debit event."""
        event_payload = event.get("payload", {})
        debit_id = event_payload.get("debit_id")

        logger.info("Processing AR debit event: %s", debit_id)

        try:
            # TODO: Implement AR debit event processing
            # self.ar_debit_event_processor.process_debit_event(message_id, event)
            logger.info("AR debit event processing not yet implemented: %s", debit_id)
        except Exception as e:
            logger.exception("Failed to process AR debit event %s: %s", debit_id, str(e))
            raise

    @session_manager(commit=True)
    def _process_reversal_event(self, message_id: str, event: Dict[str, Any]) -> None:
        """Process AR reversal event."""
        event_payload = event.get("payload", {})
        reversal_id = event_payload.get("reversal_id")
        original_transaction_id = event_payload.get("original_transaction_id")

        logger.info(
            "Processing AR reversal event: %s for transaction: %s",
            reversal_id,
            original_transaction_id,
        )

        try:
            # TODO: Implement AR reversal event processing
            # This would typically reverse the original transaction
            logger.info("AR reversal event processing not yet implemented: %s", reversal_id)
        except Exception as e:
            logger.exception("Failed to process AR reversal event %s: %s", reversal_id, str(e))
            raise
