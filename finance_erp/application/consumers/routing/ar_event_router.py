import logging
from typing import Any, Dict

from finance_erp.application.financial_transactions.event_preprocessors.ar_event_processor import (
    ARCreditEventProcessor,
    ARDebitEventProcessor,
)
from finance_erp.common.decorators import session_manager
from finance_erp.common.utils.utils import fin_erp_random_id_generator
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        ARCreditEventProcessor,
        ARDebitEventProcessor,
    ]
)
class AREventRouter:
    """
    Router for handling AR (Accounts Receivable) events.
    Processes credit events, debit events, and reversals.
    """

    # Entity names for AR events
    CREDIT_CREATED = "credit.created"
    CREDIT_REVERSED = "credit.reversed"
    CREDIT_REVERSAL_CANCELLED = "credit.reversal.cancelled"
    DEBIT_CREATED = "debit.created"

    def __init__(
        self,
        ar_credit_event_processor: ARCreditEventProcessor,
        ar_debit_event_processor: ARDebitEventProcessor,
    ):
        self.ar_credit_event_processor = ar_credit_event_processor
        self.ar_debit_event_processor = ar_debit_event_processor

    def can_handle_message(self, entity_type: str) -> bool:
        """Check if the message contains relevant AR events."""
        return entity_type in (
            self.CREDIT_CREATED,
            self.CREDIT_REVERSED,
            self.CREDIT_REVERSAL_CANCELLED,
            self.DEBIT_CREATED,
        )

    def process_ar_events(self, body: Dict[str, Any]) -> None:
        """Process AR credit events."""
        entity_type = body.get("entity_type")
        if not self.can_handle_message(entity_type=entity_type):
            logger.debug("No relevant AR credit events found in message")
            return

        message_id = body.get("message_id")
        if not message_id:
            body["message_id"] = (fin_erp_random_id_generator("MES", max_length=20),)

        try:
            if entity_type == self.CREDIT_CREATED:
                self._process_credit_created_event(message_id, body)
            elif entity_type == self.CREDIT_REVERSED:
                self._process_credit_reversal_event(message_id, body)
            elif entity_type == self.CREDIT_REVERSAL_CANCELLED:
                self._process_credit_cancel_reversal_event(message_id, body)
            elif entity_type == self.DEBIT_CREATED:
                self._process_debit_created_event(message_id, body)
        except Exception as e:
            logger.exception(
                "AR credit event router error while processing message: %s", e
            )

    @session_manager(commit=True)
    def _process_credit_created_event(
        self, message_id: str, body: Dict[str, Any]
    ) -> None:
        """Process individual AR credit event."""
        credit_id = body.get("credit_id")
        logger.info("Processing AR credit event: %s", credit_id)

        try:
            self.ar_credit_event_processor.process_credit_event(message_id, body)
        except Exception as e:
            logger.exception(
                "Failed to process AR credit event %s: %s", credit_id, str(e)
            )
            raise

    @session_manager(commit=True)
    def _process_debit_created_event(
        self, message_id: str, event: Dict[str, Any]
    ) -> None:
        """Process individual AR debit event."""
        event_payload = event.get("payload", {})
        debit_id = event_payload.get("debit_id")

        logger.info("Processing AR debit event: %s", debit_id)

        try:
            self.ar_debit_event_processor.process_debit_event(message_id, event)
        except Exception as e:
            logger.exception(
                "Failed to process AR debit event %s: %s", debit_id, str(e)
            )
            raise

    @session_manager(commit=True)
    def _process_credit_reversal_event(
        self, message_id: str, event: Dict[str, Any]
    ) -> None:
        """Process AR reversal event."""
        event_payload = event.get("payload", {})
        reversal_id = event_payload.get("reversal_id")
        original_transaction_id = event_payload.get("original_transaction_id")

        logger.info(
            "Processing AR reversal event: %s for transaction: %s",
            reversal_id,
            original_transaction_id,
        )

        try:
            # TODO: Implement AR reversal event processing
            # This would typically reverse the original transaction
            logger.info(
                "AR reversal event processing not yet implemented: %s", reversal_id
            )
        except Exception as e:
            logger.exception(
                "Failed to process AR reversal event %s: %s", reversal_id, str(e)
            )
            raise

    @session_manager(commit=True)
    def _process_credit_cancel_reversal_event(
        self, message_id: str, event: Dict[str, Any]
    ) -> None:
        """Process AR reversal event."""
        event_payload = event.get("payload", {})
        reversal_id = event_payload.get("reversal_id")
        original_transaction_id = event_payload.get("original_transaction_id")

        logger.info(
            "Processing AR reversal event: %s for transaction: %s",
            reversal_id,
            original_transaction_id,
        )

        try:
            # TODO: Implement AR reversal event processing
            # This would typically reverse the original transaction
            logger.info(
                "AR reversal event processing not yet implemented: %s", reversal_id
            )
        except Exception as e:
            logger.exception(
                "Failed to process AR reversal event %s: %s", reversal_id, str(e)
            )
            raise
