"""
Consumer routing helpers for handling different types of events.

This package contains helper classes that encapsulate the routing logic
for different event types, making the main consumers cleaner and more maintainable.
"""

from .booking_event_router import BookingEventRouter
from .night_audit_event_router import NightAuditEventRouter
from .pos_event_router import POSEventRouter

__all__ = [
    "BookingEventRouter",
    "POSEventRouter",
    "NightAuditEventRouter",
]
