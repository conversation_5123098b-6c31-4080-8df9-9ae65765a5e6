"""
Consumer routing helpers for handling different types of events.

This package contains helper classes that encapsulate the routing logic
for different event types, making the main consumers cleaner and more maintainable.
"""

from .ar_event_router import AREventRouter
from .booking_event_router import BookingEventRouter
from .night_audit_event_router import Night<PERSON><PERSON>tEventRouter
from .pos_event_router import POSEventRouter

__all__ = [
    "AREventRouter",
    "BookingEventRouter",
    "POSEventRouter",
    "NightAuditEventRouter",
]
