import logging
from typing import Any, Dict, List

from flask import current_app

from finance_erp.application.consumers.constants import (
    CRSUserActions,
    InvoiceStatusToConsume,
)
from finance_erp.application.corporate.communication.cn_dispatcher import (
    CorporateCreditNoteDispatcher,
)
from finance_erp.application.corporate.communication.invoice_dispatcher import (
    CorporateInvoiceDispatcher,
)
from finance_erp.application.crs.command_handlers.ingest_booking import (
    CrsBookingIngestionCommandHandler,
)
from finance_erp.application.financial_transactions.event_preprocessors.crs_bill_event_processor import (
    CRSBillEventProcessor,
)
from finance_erp.application.financial_transactions.event_preprocessors.crs_invoice_event_processor import (
    CRSInvoiceEventProcessor,
)
from finance_erp.application.invoice.command_handler.ingest_credit_note import (
    CreditNoteIngestionCommandHandler,
)
from finance_erp.application.invoice.command_handler.ingest_invoice import (
    InvoiceIngestionCommandHandler,
)
from finance_erp.common.decorators import session_manager
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CreditNoteIngestionCommandHandler,
        InvoiceIngestionCommandHandler,
        CrsBookingIngestionCommandHandler,
        CorporateInvoiceDispatcher,
        CorporateCreditNoteDispatcher,
        CRSBillEventProcessor,
        CRSInvoiceEventProcessor,
    ]
)
class BookingEventRouter:

    # Entity names
    INVOICE_ENTITY_NAME = "invoice"
    CREDIT_NOTE_ENTITY_NAME = "credit_note"
    BOOKING_ENTITY_NAME = "booking"
    BILL_ENTITY_NAME = "bill"

    def __init__(
        self,
        credit_note_ingestion_handler: CreditNoteIngestionCommandHandler,
        invoice_ingestion_handler: InvoiceIngestionCommandHandler,
        booking_ingestion_handler: CrsBookingIngestionCommandHandler,
        invoice_dispatcher: CorporateInvoiceDispatcher,
        credit_note_dispatcher: CorporateCreditNoteDispatcher,
        bill_event_processor: CRSBillEventProcessor,
        invoice_event_processor: CRSInvoiceEventProcessor,
    ):
        self.credit_note_ingestion_handler = credit_note_ingestion_handler
        self.invoice_ingestion_handler = invoice_ingestion_handler
        self.booking_ingestion_handler = booking_ingestion_handler
        self.invoice_dispatcher = invoice_dispatcher
        self.credit_note_dispatcher = credit_note_dispatcher
        self.bill_event_processor = bill_event_processor
        self.invoice_event_processor = invoice_event_processor

    def can_handle_message(self, body: Dict[str, Any]) -> bool:
        events = body.get("events", [])
        return any(
            event.get("entity_name")
            in (
                self.CREDIT_NOTE_ENTITY_NAME,
                self.INVOICE_ENTITY_NAME,
                self.BOOKING_ENTITY_NAME,
                self.BILL_ENTITY_NAME,
            )
            for event in events
        )

    def process_booking_events(self, body: Dict[str, Any]) -> None:
        if not self.can_handle_message(body):
            logger.debug("No relevant booking events found in message")
            return

        logger.debug("Processing booking events from CRS")
        user_action = body.get("user_action")
        message_id = body.get("message_id")

        with current_app.test_request_context():
            try:
                checked_out_booking_ids = []

                for event in body.get("events", []):
                    event_payload = event.get("payload", {})
                    entity_name = event.get("entity_name")

                    if entity_name == self.BOOKING_ENTITY_NAME:
                        self._process_booking_event(
                            event_payload, user_action, checked_out_booking_ids
                        )

                    elif entity_name == self.CREDIT_NOTE_ENTITY_NAME:
                        self._process_credit_note_event(event_payload)

                    elif entity_name == self.INVOICE_ENTITY_NAME:
                        self._process_invoice_event(message_id, event_payload)

                    elif entity_name == self.BILL_ENTITY_NAME:
                        self._process_bill_event(message_id, event)

                if checked_out_booking_ids:
                    self._dispatch_for_checked_out_bookings(checked_out_booking_ids)

            except Exception as e:
                logger.exception(
                    "Booking event router error while processing message: %s", e
                )

    def _process_booking_event(
        self,
        event_payload: Dict[str, Any],
        user_action: str,
        checked_out_booking_ids: List[str],
    ) -> None:
        booking_id = event_payload.get("booking_id")
        logger.info("Processing booking ingestion event: %s", booking_id)

        self.booking_ingestion_handler.handle(event_payload)

        booking_status = event_payload.get("status")
        if user_action == CRSUserActions.CHECKOUT and booking_status == "checked_out":
            checked_out_booking_ids.append(booking_id)

    def _process_credit_note_event(self, event_payload: Dict[str, Any]) -> None:
        logger.info(
            "Processing credit note ingestion event with payload: %s", event_payload
        )
        self.credit_note_ingestion_handler.handle(event_payload)

    def _process_invoice_event(self, message_id, event_payload: Dict[str, Any]) -> None:
        if self._is_invoice_eligible_for_ingestion(event_payload):
            logger.info(
                "Processing invoice ingestion event with payload: %s", event_payload
            )
            self.invoice_ingestion_handler.handle(event_payload)
        else:
            logger.debug(
                "Invoice not eligible for ingestion: %s",
                event_payload.get("invoice_id"),
            )

        if self._should_process_invoice_for_financial_events(event_payload):
            self._process_invoice_financial_event(message_id, event_payload)

    @session_manager(commit=True)
    def _process_bill_event(self, message_id: str, event: Dict[str, Any]) -> None:
        event_payload = event.get("payload", {})
        bill_id = event_payload.get("bill_id")

        logger.info("Processing CRS bill event: %s", bill_id)

        try:
            self.bill_event_processor.process_bill_event(message_id, event)
        except Exception as e:
            logger.exception("Failed to process CRS bill event %s: %s", bill_id, str(e))
            raise

    def _dispatch_for_checked_out_bookings(
        self, checked_out_booking_ids: List[str]
    ) -> None:
        logger.info(
            "Dispatching for %d checked out bookings", len(checked_out_booking_ids)
        )

        try:
            self.invoice_dispatcher.dispatch_for_bookings(checked_out_booking_ids)
            self.credit_note_dispatcher.dispatch_for_bookings(checked_out_booking_ids)
        except Exception as e:
            logger.exception("Failed to dispatch for checked out bookings: %s", str(e))
            raise

    @staticmethod
    def _is_invoice_eligible_for_ingestion(invoice: Dict[str, Any]) -> bool:
        return invoice.get("status") in {
            InvoiceStatusToConsume.LOCKED,
            InvoiceStatusToConsume.GENERATED,
            InvoiceStatusToConsume.CANCELLED,
        }

    @session_manager(commit=True)
    def _process_invoice_financial_event(
        self, message_id, event_payload: Dict[str, Any]
    ) -> None:
        invoice_id = event_payload.get("invoice_id")
        bill_id = event_payload.get("bill_id")

        logger.info(
            "Processing CRS invoice financial event: invoice_id=%s, bill_id=%s",
            invoice_id,
            bill_id,
        )

        try:
            event = {"entity_name": "invoice", "payload": event_payload}
            self.invoice_event_processor.process_invoice_event(message_id, event)
        except Exception as e:
            logger.exception(
                "Failed to process CRS invoice financial event %s: %s",
                invoice_id,
                str(e),
            )
            raise

    @staticmethod
    def _should_process_invoice_for_financial_events(invoice: Dict[str, Any]) -> bool:
        status = invoice.get("status")
        return status in {InvoiceStatusToConsume.LOCKED}
