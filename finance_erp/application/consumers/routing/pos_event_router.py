import logging
from typing import Any, Dict

from flask import current_app

from finance_erp.application.financial_transactions.event_preprocessors.pos_bill_event_processor import (
    POSBillEventProcessor,
)
from finance_erp.common.decorators import session_manager
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        POSBillEventProcessor,
    ]
)
class POSEventRouter:

    BILL_ENTITY_NAME = "bill"

    SUPPORTED_EVENT_TYPES = {
        "pos.bill_invoiced",
    }

    def __init__(
        self,
        bill_event_processor: POSBillEventProcessor,
    ):
        self.bill_event_processor = bill_event_processor

    def can_handle_message(self, body: Dict[str, Any]) -> bool:
        event_type = body.get("event_type")

        if event_type not in self.SUPPORTED_EVENT_TYPES:
            return False

        events = body.get("events", [])
        return any(
            event.get("entity_name") == self.BILL_ENTITY_NAME for event in events
        )

    def process_pos_events(self, body: Dict[str, Any]) -> None:
        if not self.can_handle_message(body):
            logger.debug("No relevant POS events found in message")
            return

        event_type = body.get("event_type")
        message_id = body.get("message_id")

        logger.debug("Processing POS events of type: %s", event_type)

        with current_app.test_request_context():
            try:
                for event in body.get("events", []):
                    entity_name = event.get("entity_name")

                    if entity_name == self.BILL_ENTITY_NAME:
                        self._process_bill_event(message_id, event, event_type)

            except Exception as e:
                logger.exception(
                    "POS event router error while processing message: %s", e
                )

    @session_manager(commit=True)
    def _process_bill_event(
        self, message_id: str, event: Dict[str, Any], event_type: str
    ) -> None:
        event_payload = event.get("payload", {})
        bill_id = event_payload.get("bill_id")

        logger.info("Processing POS bill event: %s (type: %s)", bill_id, event_type)

        try:
            self.bill_event_processor.process_bill_event(message_id, event)
        except Exception as e:
            logger.exception("Failed to process POS bill event %s: %s", bill_id, str(e))
            raise

    def get_supported_event_types(self) -> set:
        return self.SUPPORTED_EVENT_TYPES.copy()

    def is_event_type_supported(self, event_type: str) -> bool:
        return event_type in self.SUPPORTED_EVENT_TYPES
