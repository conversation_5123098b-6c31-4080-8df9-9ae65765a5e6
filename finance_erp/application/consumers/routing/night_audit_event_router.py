import logging
from typing import Any, Dict

from flask import current_app

from finance_erp.application.financial_transactions.event_preprocessors.night_audit_event_processor import (
    NightAuditEventProcessor,
)
from finance_erp.common.decorators import session_manager
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        NightAuditEventProcessor,
    ]
)
class NightAuditEventRouter:

    NIGHT_AUDIT_ENTITY_NAME = "night_audit"

    SUPPORTED_EVENT_TYPES = {
        "night_audit.completed",
    }

    def __init__(
        self,
        night_audit_event_processor: NightAuditEventProcessor,
    ):
        self.night_audit_event_processor = night_audit_event_processor

    def can_handle_message(self, body: Dict[str, Any]) -> bool:
        event_type = body.get("event_type")

        if event_type not in self.SUPPORTED_EVENT_TYPES:
            return False

        events = body.get("events", [])
        return any(
            event.get("entity_name") == self.NIGHT_AUDIT_ENTITY_NAME for event in events
        )

    def process_night_audit_events(self, body: Dict[str, Any]) -> None:
        """Process Night Audit events"""
        if not self.can_handle_message(body):
            logger.debug("No relevant Night Audit events found in message")
            return

        event_type = body.get("event_type")
        message_id = body.get("message_id")

        logger.debug("Processing Night Audit events of type: %s", event_type)

        with current_app.test_request_context():
            try:
                for event in body.get("events", []):
                    entity_name = event.get("entity_name")

                    if entity_name == self.NIGHT_AUDIT_ENTITY_NAME:
                        self._process_night_audit_event(message_id, event, event_type)

            except Exception as e:
                logger.exception(
                    "Night Audit event router error while processing message: %s", e
                )

    @session_manager(commit=True)
    def _process_night_audit_event(
        self, message_id: str, event: Dict[str, Any], event_type: str
    ) -> None:
        event_payload = event.get("payload", {})
        night_audit_id = event_payload.get("night_audit_id")
        hotel_id = event_payload.get("hotel_id")

        logger.info(
            "Processing Night Audit event: %s for hotel: %s (type: %s)",
            night_audit_id,
            hotel_id,
            event_type,
        )

        try:
            self.night_audit_event_processor.process_night_audit_event(
                message_id, event
            )
        except Exception as e:
            logger.exception(
                "Failed to process Night Audit event %s: %s", night_audit_id, str(e)
            )
            raise
