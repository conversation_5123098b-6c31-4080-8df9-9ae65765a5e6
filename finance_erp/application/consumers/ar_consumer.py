import logging

from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from finance_erp.application.consumers.routing import AREventRouter
from finance_erp.common.decorators import consumer_middleware
from finance_erp.infrastructure.consumers.base_consumer import BaseRMQConsumer
from finance_erp.infrastructure.consumers.consumer_config import ARConsumerConfig

logger = logging.getLogger(__name__)


class ARConsumer(BaseRMQConsumer):
    def __init__(
        self,
        ar_event_router: AREventRouter,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(ARConsumerConfig(tenant_id))
        self.tenant_id = tenant_id
        self.ar_event_router = ar_event_router

        logger.info(
            "AR Consumer initialized - Listening to RMQ on host: %s from queues: %s",
            self.connection,
            [queue.name for queue in self.queues],
        )

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        message.ack()
        routing_key = message.delivery_info.get("routing_key")

        logger.debug("Processing AR message with routing key: %s", routing_key)

        try:
            # Route AR events based on routing key pattern
            if routing_key.startswith("ar.events.credit"):
                return self.ar_event_router.process_credit_events(body)
            elif routing_key.startswith("ar.events.debit"):
                return self.ar_event_router.process_debit_events(body)
            else:
                logger.warning("Unknown AR routing key: %s", routing_key)
                return None
        except Exception as e:
            logger.exception(
                "Failed to process AR message with routing key %s: %s", routing_key, str(e)
            )
