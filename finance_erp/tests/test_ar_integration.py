"""
Integration test for AR event processing.
Tests the complete flow from AR event consumption to transaction generation.
"""
from datetime import date
from unittest.mock import Mock, patch

import pytest

from finance_erp.application.consumers.routing.ar_event_router import AREventRouter
from finance_erp.application.financial_transactions.event_preprocessors.ar_event_processor import (
    ARCreditEventProcessor,
    ARDebitEventProcessor,
)
from finance_erp.domain.financial_transactions.constants import FinancialEventTypes


class TestARIntegration:
    """Test AR event processing integration."""

    def test_ar_credit_event_processing(self):
        """Test processing of AR credit event."""
        # Sample AR credit event payload
        ar_credit_event = {
            "message_id": "test-msg-001",
            "events": [
                {
                    "entity_name": "credit",
                    "payload": {
                        "credit_id": "CR-001",
                        "transaction_id": "TXN-001",
                        "hotel_id": "HTL-001",
                        "amount": 1000.0,
                        "currency": "INR",
                        "posting_date": "2023-12-01",
                        "reference_number": "REF-001",
                        "status": "created",
                    },
                }
            ],
        }

        # Mock dependencies
        with patch(
            "finance_erp.application.financial_transactions.event_preprocessors.ar_event_processor.AREventDecomposer"
        ) as mock_decomposer, patch(
            "finance_erp.domain.financial_transactions.repository.source_system_event_repository.SourceSystemEventRepository"
        ) as mock_source_repo, patch(
            "finance_erp.domain.financial_transactions.repository.financial_event_repository.FinancialEventRepository"
        ) as mock_financial_repo, patch(
            "finance_erp.application.financial_transactions.transaction_engine.TransactionEngine"
        ) as mock_transaction_engine:

            # Create processor
            processor = ARCreditEventProcessor(
                mock_decomposer,
                mock_source_repo,
                mock_financial_repo,
                mock_transaction_engine,
            )

            # Mock the process_bill_event method to return mock financial events
            mock_financial_events = [Mock()]
            mock_financial_events[0].financial_event_id = "FE-001"

            with patch.object(
                processor, "process_bill_event", return_value=mock_financial_events
            ):
                # Process the event
                result = processor.process_credit_event(
                    ar_credit_event["message_id"], ar_credit_event["events"][0]
                )

                # Verify transaction engine was called
                mock_transaction_engine.process.assert_called_once()

    def test_ar_debit_event_processing(self):
        """Test processing of AR debit event."""
        # Sample AR debit event payload
        ar_debit_event = {
            "message_id": "test-msg-002",
            "events": [
                {
                    "entity_name": "debit",
                    "payload": {
                        "debit_id": "DR-001",
                        "transaction_id": "TXN-002",
                        "hotel_id": "HTL-001",
                        "amount": 500.0,
                        "currency": "INR",
                        "posting_date": "2023-12-01",
                        "reference_number": "REF-002",
                        "status": "created",
                    },
                }
            ],
        }

        # Mock dependencies
        with patch(
            "finance_erp.application.financial_transactions.event_preprocessors.ar_event_processor.AREventDecomposer"
        ) as mock_decomposer, patch(
            "finance_erp.domain.financial_transactions.repository.source_system_event_repository.SourceSystemEventRepository"
        ) as mock_source_repo, patch(
            "finance_erp.domain.financial_transactions.repository.financial_event_repository.FinancialEventRepository"
        ) as mock_financial_repo, patch(
            "finance_erp.application.financial_transactions.transaction_engine.TransactionEngine"
        ) as mock_transaction_engine:

            # Create processor
            processor = ARDebitEventProcessor(
                mock_decomposer,
                mock_source_repo,
                mock_financial_repo,
                mock_transaction_engine,
            )

            # Mock the process_bill_event method to return mock financial events
            mock_financial_events = [Mock()]
            mock_financial_events[0].financial_event_id = "FE-002"

            with patch.object(
                processor, "process_bill_event", return_value=mock_financial_events
            ):
                # Process the event
                result = processor.process_debit_event(
                    ar_debit_event["message_id"], ar_debit_event["events"][0]
                )

                # Verify transaction engine was called
                mock_transaction_engine.process.assert_called_once()

    def test_ar_event_router_credit_routing(self):
        """Test AR event router correctly routes credit events."""
        # Mock processors
        mock_credit_processor = Mock()
        mock_debit_processor = Mock()

        # Create router
        router = AREventRouter(mock_credit_processor, mock_debit_processor)

        # Sample credit event message
        credit_message = {
            "message_id": "test-msg-003",
            "events": [
                {
                    "entity_name": "credit",
                    "payload": {
                        "credit_id": "CR-002",
                        "amount": 750.0,
                    },
                }
            ],
        }

        # Process credit events
        router.process_credit_events(credit_message)

        # Verify credit processor was called
        mock_credit_processor.process_credit_event.assert_called_once()
        mock_debit_processor.process_debit_event.assert_not_called()

    def test_ar_event_router_debit_routing(self):
        """Test AR event router correctly routes debit events."""
        # Mock processors
        mock_credit_processor = Mock()
        mock_debit_processor = Mock()

        # Create router
        router = AREventRouter(mock_credit_processor, mock_debit_processor)

        # Sample debit event message
        debit_message = {
            "message_id": "test-msg-004",
            "events": [
                {
                    "entity_name": "debit",
                    "payload": {
                        "debit_id": "DR-002",
                        "amount": 300.0,
                    },
                }
            ],
        }

        # Process debit events
        router.process_debit_events(debit_message)

        # Verify debit processor was called
        mock_debit_processor.process_debit_event.assert_called_once()
        mock_credit_processor.process_credit_event.assert_not_called()

    def test_ar_financial_event_types_defined(self):
        """Test that AR financial event types are properly defined."""
        assert hasattr(FinancialEventTypes, "AR_CREDIT")
        assert hasattr(FinancialEventTypes, "AR_DEBIT")
        assert hasattr(FinancialEventTypes, "AR_REVERSAL")

        assert FinancialEventTypes.AR_CREDIT == "ar_credit"
        assert FinancialEventTypes.AR_DEBIT == "ar_debit"
        assert FinancialEventTypes.AR_REVERSAL == "ar_reversal"
