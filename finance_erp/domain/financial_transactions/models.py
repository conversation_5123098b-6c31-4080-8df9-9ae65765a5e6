from typing import Dict

from sqlalchemy import (
    Boolean,
    Column,
    Date,
    DateTime,
    Index,
    Integer,
    Numeric,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from finance_erp.domain.shared_kernel.orm_base import DeleteMixin, TimeStampMixin


class SourceSystemEventModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "source_system_events"

    event_id = Column(String, primary_key=True)
    message_id = Column(String, nullable=False, unique=True)
    source_system = Column(String, nullable=False)
    event_type = Column(String, nullable=False)
    hotel_id = Column(String, nullable=True)
    system_scope = Column(String, nullable=False, default="hotel")
    entity_id = Column(String, nullable=True)
    event_payload = Column(JSONB, nullable=False)
    received_at = Column(DateTime(timezone=True), nullable=False)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    processing_status = Column(String, nullable=False, default="received")
    failure_reason = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    source_system_metadata = Column(JSONB, nullable=True)
    version_id = Column(Integer, nullable=True)

    __table_args__ = (
        Index("idx_source_events_system_type", "source_system", "event_type"),
        Index("idx_source_events_hotel_received", "hotel_id", "received_at"),
        Index("idx_source_events_status", "processing_status"),
        Index("idx_source_events_scope", "system_scope"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "event_id": self.event_id,
            "message_id": self.message_id,
            "source_system": self.source_system,
            "event_type": self.event_type,
            "hotel_id": self.hotel_id,
            "system_scope": self.system_scope,
            "entity_id": self.entity_id,
            "event_payload": self.event_payload,
            "received_at": self.received_at,
            "processed_at": self.processed_at,
            "processing_status": self.processing_status,
            "failure_reason": self.failure_reason,
            "retry_count": self.retry_count,
            "source_system_metadata": self.source_system_metadata,
            "version_id": self.version_id,
        }


class FinancialEventModel(Base, TimeStampMixin):
    __tablename__ = "financial_events"

    financial_event_id = Column(String, primary_key=True)
    parent_event_id = Column(String, nullable=False)
    source_system = Column(String, nullable=False)
    entity_type = Column(String, nullable=False)
    entity_id = Column(String, nullable=False)
    document_number = Column(String, nullable=True)
    hotel_id = Column(String, nullable=False)
    posting_date = Column(Date)
    event_data = Column(JSONB, nullable=False)
    processing_status = Column(String, nullable=False, default="pending")
    source_system_status = Column(String)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    system_scope = Column(String, nullable=False, default="hotel")
    source_system_metadata = Column(JSONB, nullable=True)

    related_charge_id = Column(String, nullable=True)
    related_charge_split_id = Column(String, nullable=True)

    __table_args__ = (
        Index("idx_financial_events_posting_date", "hotel_id", "posting_date"),
        Index("idx_financial_events_entity_type", "entity_type", "processing_status"),
        Index("idx_financial_events_parent", "parent_event_id"),
        Index("idx_financial_events_source_system", "source_system", "entity_type"),
        Index("idx_financial_events_document_number", "document_number"),
        Index("idx_financial_events_scope", "system_scope"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "financial_event_id": self.financial_event_id,
            "parent_event_id": self.parent_event_id,
            "source_system": self.source_system,
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "document_number": self.document_number,
            "hotel_id": self.hotel_id,
            "posting_date": self.posting_date,
            "event_data": self.event_data,
            "processing_status": self.processing_status,
            "source_system_status": self.source_system_status,
            "processed_at": self.processed_at,
            "system_scope": self.system_scope,
            "source_system_metadata": self.source_system_metadata,
            "related_charge_id": self.related_charge_id,
            "related_charge_split_id": self.related_charge_split_id,
        }


class TransactionModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "transactions"

    transaction_id = Column(String, primary_key=True)
    financial_event_id = Column(String, nullable=False)
    source_system = Column(String, nullable=False)
    source_entity_id = Column(String, nullable=True)
    hotel_id = Column(String, nullable=False)
    posting_date = Column(Date, nullable=False)
    triggering_source_type = Column(String, nullable=True)
    document_number = Column(String, nullable=True)

    txn_code = Column(String, nullable=True)
    gl_code = Column(String, nullable=True)
    erp_id = Column(String, nullable=True)
    operational_unit = Column(String, nullable=True)
    source = Column(String, nullable=True)
    accountable_entity_type = Column(String)

    txn_amount = Column(Numeric(precision=10, scale=2), nullable=False)
    txn_side = Column(String, nullable=False)

    particular = Column(Text, nullable=True)
    txn_group = Column(String, nullable=True)
    is_mergeable = Column(Boolean, default=True)

    processing_status = Column(String, nullable=False, default="pending")
    processed_at = Column(DateTime(timezone=True), nullable=True)
    aggregated_transaction_id = Column(String, nullable=True)

    txn_metadata = Column(JSONB, nullable=True)

    __table_args__ = (
        Index("idx_transactions_hotel_date", "hotel_id", "posting_date"),
        Index("idx_transactions_financial_event", "financial_event_id"),
        Index("idx_transactions_status", "processing_status"),
        Index("idx_transactions_mergeable", "is_mergeable", "txn_code", "gl_code"),
        Index("idx_transactions_source", "source_system", "source_entity_id"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "transaction_id": self.transaction_id,
            "financial_event_id": self.financial_event_id,
            "source_system": self.source_system,
            "source_entity_id": self.source_entity_id,
            "hotel_id": self.hotel_id,
            "posting_date": self.posting_date,
            "triggering_source_type": self.triggering_source_type,
            "document_number": self.document_number,
            "txn_code": self.txn_code,
            "gl_code": self.gl_code,
            "erp_id": self.erp_id,
            "operational_unit": self.operational_unit,
            "source": self.source,
            "accountable_entity_type": self.accountable_entity_type,
            "txn_amount": self.txn_amount,
            "txn_side": self.txn_side,
            "particular": self.particular,
            "txn_group": self.txn_group,
            "is_mergeable": self.is_mergeable,
            "processing_status": self.processing_status,
            "processed_at": self.processed_at,
            "aggregated_transaction_id": self.aggregated_transaction_id,
            "txn_metadata": self.txn_metadata,
            "created_at": self.created_at,
            "modified_at": self.modified_at,
            "deleted": self.deleted,
        }


class AggregatedTransactionModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "aggregated_transactions"

    aggregated_transaction_id = Column(String, primary_key=True)
    hotel_id = Column(String, nullable=False)
    posting_date = Column(Date)

    txn_code = Column(String, nullable=True)
    gl_code = Column(String, nullable=True)
    erp_id = Column(String, nullable=True)
    operational_unit = Column(String, nullable=True)
    source = Column(String, nullable=True)

    net_amount = Column(Numeric(precision=10, scale=2), nullable=False)

    particular = Column(Text, nullable=True)
    txn_group = Column(String, nullable=True)
    transaction_count = Column(Integer, nullable=False, default=1)
    txn_side = Column(String)
    accountable_entity_type = Column(String)
    source_systems = Column(JSONB, nullable=True)

    processing_status = Column(String, nullable=False, default="pending")
    exported_at = Column(DateTime(timezone=True), nullable=True)
    export_batch_id = Column(String, nullable=True)

    txn_metadata = Column(JSONB, nullable=True)

    __table_args__ = (
        Index("idx_aggregated_transactions_hotel_date", "hotel_id", "posting_date"),
        Index("idx_aggregated_transactions_status", "processing_status"),
        Index("idx_aggregated_transactions_export", "export_batch_id"),
        Index("idx_aggregated_transactions_codes", "txn_code", "gl_code"),
    )

    def mapping_dict(self) -> Dict:
        return {
            "aggregated_transaction_id": self.aggregated_transaction_id,
            "hotel_id": self.hotel_id,
            "posting_date": self.posting_date,
            "txn_code": self.txn_code,
            "gl_code": self.gl_code,
            "erp_id": self.erp_id,
            "operational_unit": self.operational_unit,
            "source": self.source,
            "net_amount": self.net_amount,
            "particular": self.particular,
            "txn_group": self.txn_group,
            "txn_side": self.txn_side,
            "accountable_entity_type": self.accountable_entity_type,
            "transaction_count": self.transaction_count,
            "source_systems": self.source_systems,
            "processing_status": self.processing_status,
            "exported_at": self.exported_at,
            "export_batch_id": self.export_batch_id,
            "txn_metadata": self.txn_metadata,
            "created_at": self.created_at,
            "modified_at": self.modified_at,
            "deleted": self.deleted,
        }
