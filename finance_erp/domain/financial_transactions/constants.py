from enum import Enum


class AccountableEntityTypes:
    FRANCHISER = "franchiser"
    HOTEL = "hotel"


class FinancialEventTypes:
    CRS_CHARGE = "crs_charge"
    CRS_PAYMENT = "crs_payment"
    CRS_ALLOWANCE = "crs_allowance"
    CRS_INVOICED_CHARGE = "crs_invoiced_charge"
    CRS_INVOICED_PAYMENT = "crs_invoiced_payment"
    CRS_INVOICED_ALLOWANCE = "crs_invoiced_allowance"
    POS_CHARGE = "pos_charge"
    POS_PAYMENT = "pos_payment"


class TransactionSide:
    SELL = "sell"
    BUY = "buy"


class ProcessingStatus(Enum):
    RECEIVED = "received"
    PROCESSING = "processing"
    PENDING = "pending"
    PROCESSED = "processed"
    FAILED = "failed"
    PARTIAL_FAILURE = "partial_failure"


class SourceSystemStatus(Enum):
    """Enum for source system status values"""

    CREATED = "created"
    POSTED = "posted"
    CONSUMED = "consumed"
