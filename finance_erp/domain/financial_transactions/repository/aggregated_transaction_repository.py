from typing import List

from finance_erp.domain.financial_transactions.adaptors.transaction_adaptor import (
    AggregatedTransactionAdaptor,
)
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    AggregatedTransactionEntity,
)
from finance_erp.domain.financial_transactions.models import AggregatedTransactionModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class AggregatedTransactionRepository(BaseRepository):

    adaptor = AggregatedTransactionAdaptor()

    def to_entity(
        self, model: AggregatedTransactionModel
    ) -> AggregatedTransactionEntity:
        return self.adaptor.to_domain_entity(model)

    def from_entity(
        self, entity: AggregatedTransactionEntity
    ) -> AggregatedTransactionModel:
        return self.adaptor.to_db_entity(entity)

    def insert_many(self, entities: List[AggregatedTransactionEntity]):

        self._bulk_insert_mappings(
            AggregatedTransactionModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()

    def update_many(self, entities: List[AggregatedTransactionEntity]):

        self._bulk_update_mappings(
            AggregatedTransactionModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()
