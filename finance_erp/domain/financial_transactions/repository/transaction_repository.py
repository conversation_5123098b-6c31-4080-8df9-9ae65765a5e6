from datetime import date, datetime
from typing import List

from finance_erp.domain.financial_transactions.adaptors.transaction_adaptor import (
    TransactionAdaptor,
)
from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    TransactionEntity,
)
from finance_erp.domain.financial_transactions.models import TransactionModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from finance_erp.infrastructure.database.bulk_operations_mixin import (
    BulkOperationsMixin,
)
from object_registry import register_instance


@register_instance()
class TransactionRepository(BaseRepository, BulkOperationsMixin):

    adaptor = TransactionAdaptor()

    def to_entity(self, model: TransactionModel) -> TransactionEntity:
        return self.adaptor.to_domain_entity(model)

    def from_entity(self, entity: TransactionEntity) -> TransactionModel:
        return self.adaptor.to_db_entity(entity)

    def find_pending_for_aggregation(
        self,
        hotel_id: str,
        posting_date: date,
    ) -> List[TransactionEntity]:
        query = (
            self.session()
            .query(TransactionModel)
            .filter(
                TransactionModel.hotel_id == hotel_id,
                TransactionModel.posting_date == posting_date,
                TransactionModel.processing_status == ProcessingStatus.PENDING.value,
                TransactionModel.is_mergeable == True,
            )
        )
        models = query.all()
        return [self.to_entity(model) for model in models]

    def insert_many(self, entities: List[TransactionEntity]):
        self._bulk_insert_mappings(
            TransactionModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()

    def get_model_class(self):
        return TransactionModel

    def get_id_field_name(self) -> str:
        return "transaction_id"

    def get_id_from_entity(self, entity: TransactionEntity) -> str:
        return entity.transaction_id

    def update_many(self, entities: List[TransactionEntity]):
        self._bulk_update_mappings(
            TransactionModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()
