from datetime import date, datetime
from typing import Dict, List, Optional

from finance_erp.domain.financial_transactions.adaptors.source_system_event_adaptor import (
    FinancialEventAdaptor,
)
from finance_erp.domain.financial_transactions.constants import (
    ProcessingStatus,
    SourceSystemStatus,
)
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.models import FinancialEventModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from finance_erp.infrastructure.database.bulk_operations_mixin import (
    BulkOperationsMixin,
)
from object_registry import register_instance


@register_instance()
class FinancialEventRepository(BaseRepository, BulkOperationsMixin):

    adaptor = FinancialEventAdaptor()

    def to_entity(self, model: FinancialEventModel) -> FinancialEventEntity:
        return self.adaptor.to_domain_entity(model)

    def from_entity(self, entity: FinancialEventEntity) -> FinancialEventModel:
        return self.adaptor.to_db_entity(entity)

    def save(self, entity: FinancialEventEntity) -> FinancialEventEntity:
        entity.created_at = entity.created_at or datetime.utcnow()
        entity.modified_at = datetime.utcnow()
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def update(self, entity: FinancialEventEntity) -> FinancialEventEntity:
        entity.modified_at = datetime.utcnow()
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def find_by_id(self, financial_event_id: str) -> Optional[FinancialEventEntity]:
        model = super().get_one(
            FinancialEventModel, financial_event_id=financial_event_id
        )
        return self.to_entity(model) if model else None

    def find_pending_events(
        self,
        hotel_id: str,
        posting_date: date,
        entity_type: str = None,
        source_system: str = None,
    ) -> List[FinancialEventEntity]:
        query = (
            self.session()
            .query(FinancialEventModel)
            .filter(
                FinancialEventModel.hotel_id == hotel_id,
                FinancialEventModel.posting_date == posting_date,
                FinancialEventModel.processing_status == ProcessingStatus.PENDING.value,
                FinancialEventModel.source_system_status
                == SourceSystemStatus.POSTED.value,
            )
        )

        if entity_type:
            query = query.filter(FinancialEventModel.entity_type == entity_type)

        if source_system:
            query = query.filter(FinancialEventModel.source_system == source_system)

        query = query.order_by(FinancialEventModel.created_at)

        models = query.all()
        return [self.to_entity(model) for model in models]

    def insert_many(self, entities: List[FinancialEventEntity]):
        self._bulk_insert_mappings(
            FinancialEventModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()

    def update_many(self, entities: List[FinancialEventEntity]):
        self._bulk_update_mappings(
            FinancialEventModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()

    def get_model_class(self):
        return FinancialEventModel

    def get_id_field_name(self) -> str:
        return "financial_event_id"

    def get_id_from_entity(self, entity: FinancialEventEntity) -> str:
        return entity.financial_event_id

    def find_by_financial_event_ids(
        self, financial_event_ids: List[str]
    ) -> List[FinancialEventEntity]:
        if not financial_event_ids:
            return []

        models = (
            self.session()
            .query(FinancialEventModel)
            .filter(FinancialEventModel.financial_event_id.in_(financial_event_ids))
            .all()
        )
        return [self.to_entity(model) for model in models]

    def check_events_existence(self, financial_event_ids: List[str]) -> Dict[str, bool]:
        if not financial_event_ids:
            return {}

        existing_ids = (
            self.session()
            .query(FinancialEventModel.financial_event_id)
            .filter(FinancialEventModel.financial_event_id.in_(financial_event_ids))
            .all()
        )

        existing_set = {row[0] for row in existing_ids}
        return {event_id: event_id in existing_set for event_id in financial_event_ids}

    def find_by_invoice_id(self, invoice_id: str) -> List[FinancialEventEntity]:
        """Find financial events that were created for a specific invoice"""
        if not invoice_id:
            return []

        models = (
            self.session()
            .query(FinancialEventModel)
            .filter(
                FinancialEventModel.source_system_metadata.op("->>")("invoice_id")
                == invoice_id
            )
            .order_by(FinancialEventModel.created_at)
            .all()
        )
        return [self.to_entity(model) for model in models]
