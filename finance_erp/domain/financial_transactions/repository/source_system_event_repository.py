from datetime import datetime
from typing import List, Optional

from sqlalchemy import func

from finance_erp.domain.financial_transactions.adaptors.source_system_event_adaptor import (
    SourceSystemEventAdaptor,
)
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    SourceSystemEventEntity,
)
from finance_erp.domain.financial_transactions.models import SourceSystemEventModel
from finance_erp.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class SourceSystemEventRepository(BaseRepository):

    adaptor = SourceSystemEventAdaptor()

    def to_entity(self, model: SourceSystemEventModel) -> SourceSystemEventEntity:
        return self.adaptor.to_domain_entity(model)

    def from_entity(self, entity: SourceSystemEventEntity) -> SourceSystemEventModel:
        return self.adaptor.to_db_entity(entity)

    def save(self, entity: SourceSystemEventEntity) -> SourceSystemEventEntity:
        entity.created_at = entity.created_at or datetime.utcnow()
        model = self.from_entity(entity)
        self._save(model)
        return self.to_entity(model)

    def update(self, entity: SourceSystemEventEntity) -> SourceSystemEventEntity:
        model = self.from_entity(entity)
        self._update(model)
        return self.to_entity(model)

    def find_by_message_id(self, message_id: str) -> Optional[SourceSystemEventEntity]:
        model = super().get_one(SourceSystemEventModel, message_id=message_id)
        return self.to_entity(model) if model else None

    def find_by_event_id(self, event_id: str) -> Optional[SourceSystemEventEntity]:
        model = super().get_one(SourceSystemEventModel, event_id=event_id)
        return self.to_entity(model) if model else None

    def insert_many(self, entities: List[SourceSystemEventEntity]):

        self._bulk_insert_mappings(
            SourceSystemEventModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()

    def update_many(self, entities: List[SourceSystemEventEntity]):
        self._bulk_update_mappings(
            SourceSystemEventModel,
            [self.from_entity(entity).mapping_dict() for entity in entities],
        )
        self.flush_session()

    def get_max_version_for_entity(
        self,
        entity_id: str,
        source_system: str = None,
        event_type: str = None,
        processing_status: str = None,
    ) -> Optional[int]:

        query = self.query(func.max(SourceSystemEventModel.version_id)).filter(
            SourceSystemEventModel.entity_id == entity_id,
            SourceSystemEventModel.deleted.is_(False),
        )

        if processing_status:
            query = query.filter(
                SourceSystemEventModel.processing_status == processing_status
            )

        if source_system:
            query = query.filter(SourceSystemEventModel.source_system == source_system)

        if event_type:
            query = query.filter(SourceSystemEventModel.event_type == event_type)

        result = query.scalar()
        return result
