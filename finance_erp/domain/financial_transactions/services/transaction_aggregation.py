import logging
from typing import Dict, List

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    AggregatedTransactionEntity,
    TransactionEntity,
    TransactionGroupKey,
)

logger = logging.getLogger(__name__)


class TransactionAggregationService:
    @classmethod
    def aggregate_transactions(
        cls,
        transactions: List[TransactionEntity],
        hotel_id: str,
    ) -> List[AggregatedTransactionEntity]:

        aggregated_transactions = []
        merge_map: Dict[TransactionGroupKey, AggregatedTransactionEntity] = dict()

        for transaction in transactions:
            if not transaction.is_mergeable:
                # Non-mergeable transactions go directly to results
                aggregated_transactions.append(
                    cls._create_aggregate(transaction, hotel_id)
                )
                continue

            merge_key = transaction.get_merge_key()
            existing = merge_map.get(merge_key)

            if existing is None:
                merge_map[merge_key] = cls._create_aggregate(transaction, hotel_id)
            else:
                existing.net_amount += transaction.txn_amount
                existing.transaction_count += 1
                existing.metadata["transaction_ids"].append(transaction.transaction_id)
                if transaction.source_system not in existing.source_systems:
                    existing.source_systems.append(transaction.source_system)

        # Add merged transactions to results
        aggregated_transactions.extend(merge_map.values())

        logger.info(
            "Aggregated %d transactions into %d aggregated transactions",
            len(transactions),
            len(aggregated_transactions),
        )

        return aggregated_transactions

    @staticmethod
    def _create_aggregate(
        transaction: TransactionEntity,
        hotel_id: str,
    ) -> AggregatedTransactionEntity:

        return AggregatedTransactionEntity(
            aggregated_transaction_id=fin_erp_random_id_generator("ATX", max_length=20),
            hotel_id=hotel_id,
            posting_date=transaction.posting_date,
            txn_code=transaction.txn_code,
            gl_code=transaction.gl_code,
            erp_id=transaction.erp_id,
            operational_unit=transaction.operational_unit,
            source=transaction.source,
            net_amount=transaction.txn_amount,
            particular=transaction.particular,
            txn_group=transaction.txn_group,
            transaction_count=1,
            source_systems=[transaction.source_system],
            processing_status=ProcessingStatus.PENDING,
            metadata={
                "transaction_ids": [transaction.transaction_id],
                "merge_key": str(transaction.get_merge_key()),
            },
        )
