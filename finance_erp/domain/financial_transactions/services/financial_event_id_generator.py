from typing import Callable, Dict

from finance_erp.domain.financial_transactions.constants import FinancialEventTypes


class FinancialEventIdGenerator:

    id_prefixes = {
        FinancialEventTypes.CRS_CHARGE: "CRS-CH",
        FinancialEventTypes.CRS_PAYMENT: "CRS-PY",
        FinancialEventTypes.CRS_ALLOWANCE: "CRS-ALW",
        FinancialEventTypes.CRS_INVOICED_CHARGE: "CRS-INV-CH",
        FinancialEventTypes.CRS_INVOICED_PAYMENT: "CRS-INV-PY",
        FinancialEventTypes.CRS_INVOICED_ALLOWANCE: "CRS-INV-ALW",
        FinancialEventTypes.POS_CHARGE: "POS-CH",
        FinancialEventTypes.POS_PAYMENT: "POS-PY",
    }

    @classmethod
    def _get_generator(cls, event_type: str):
        _generator: Dict[str, Callable] = {
            FinancialEventTypes.CRS_CHARGE: cls._generate_crs_charge_id,
            FinancialEventTypes.CRS_PAYMENT: cls._generate_crs_payment_id,
            FinancialEventTypes.CRS_ALLOWANCE: cls._generate_crs_allowance_id,
            FinancialEventTypes.CRS_INVOICED_CHARGE: cls._generate_crs_invoiced_charge_id,
            FinancialEventTypes.CRS_INVOICED_PAYMENT: cls._generate_crs_invoiced_payment_id,
            FinancialEventTypes.CRS_INVOICED_ALLOWANCE: cls._generate_crs_invoiced_allowance_id,
            FinancialEventTypes.POS_CHARGE: cls._generate_pos_charge_id,
            FinancialEventTypes.POS_PAYMENT: cls._generate_pos_payment_id,
        }
        return _generator[event_type]

    @classmethod
    def generate_id(cls, entity_type: str, **kwargs) -> str:
        generator = cls._get_generator(entity_type)
        if not generator:
            raise ValueError(f"No ID generator found for entity type: {entity_type}")

        return generator(**kwargs)

    @staticmethod
    def _generate_crs_charge_id(bill_id: str, charge_id: str, **kwargs) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[FinancialEventTypes.CRS_CHARGE]
        return f"{prefix}-{bill_id}-{charge_id}"

    @staticmethod
    def _generate_crs_payment_id(bill_id: str, payment_id: str, **kwargs) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[FinancialEventTypes.CRS_PAYMENT]
        return f"{prefix}-{bill_id}-{payment_id}"

    @staticmethod
    def _generate_crs_allowance_id(
        bill_id: str, charge_id: str, charge_split_id: str, allowance_id: str, **kwargs
    ) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[
            FinancialEventTypes.CRS_ALLOWANCE
        ]
        return f"{prefix}-{bill_id}-{charge_id}-" f"{charge_split_id}-{allowance_id}"

    @staticmethod
    def _generate_pos_charge_id(bill_id: str, charge_id: str, **kwargs) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[FinancialEventTypes.POS_CHARGE]
        return f"{prefix}-{bill_id}-{charge_id}"

    @staticmethod
    def _generate_pos_payment_id(bill_id: str, payment_id: str, **kwargs) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[FinancialEventTypes.POS_PAYMENT]
        return f"{prefix}-{bill_id}-{payment_id}"

    @staticmethod
    def _generate_crs_invoiced_charge_id(
        bill_id: str, charge_id: str, charge_split_id: str, invoice_id: str, **kwargs
    ) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[
            FinancialEventTypes.CRS_INVOICED_CHARGE
        ]
        return f"{prefix}-{bill_id}-{charge_id}-{charge_split_id}-{invoice_id}"

    @staticmethod
    def _generate_crs_invoiced_payment_id(
        bill_id: str, payment_id: str, payment_split_id: str, invoice_id: str, **kwargs
    ) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[
            FinancialEventTypes.CRS_INVOICED_PAYMENT
        ]
        return f"{prefix}-{bill_id}-{payment_id}-{payment_split_id}-{invoice_id}"

    @staticmethod
    def _generate_crs_invoiced_allowance_id(
        bill_id: str,
        charge_id: str,
        charge_split_id: str,
        allowance_id: str,
        invoice_id: str,
        **kwargs,
    ) -> str:
        prefix = FinancialEventIdGenerator.id_prefixes[
            FinancialEventTypes.CRS_INVOICED_ALLOWANCE
        ]
        return f"{prefix}-{bill_id}-{charge_id}-{charge_split_id}-{allowance_id}-{invoice_id}"
