import copy
from decimal import Decimal
from typing import Any, Dict, List

from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.base_transformer import (
    ResellerTransformer,
    TransformationError,
)
from object_registry import register_instance


@register_instance()
class ChargeResellerTransformer(ResellerTransformer):
    def transform_to_buy_side(
        self, sell_event: FinancialEventEntity
    ) -> FinancialEventEntity:
        try:
            # Create base buy-side event
            buy_side_event = self._create_base_buy_side_event(sell_event)

            # Transform charge-specific data
            buy_side_event.event_data = self._transform_charge_data(
                sell_event.event_data
            )

            return buy_side_event

        except Exception as e:
            raise TransformationError(
                message=str(e),
                sell_event_id=sell_event.financial_event_id,
                transformation_type="charge",
            )

    def _transform_charge_data(
        self, sell_charge_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        # Deep copy to avoid modifying original data
        buy_charge_data = copy.deepcopy(sell_charge_data)

        # Transform amounts (pretax and tax components)
        original_pretax = Decimal(str(sell_charge_data.get("pretax_amount", 0)))
        original_tax_details = sell_charge_data.get("tax_details", [])

        new_pretax, new_tax_details = self._transform_amounts(
            original_pretax, original_tax_details
        )

        # Update transformed amounts
        buy_charge_data["pretax_amount"] = float(new_pretax)
        buy_charge_data["tax_details"] = new_tax_details

        # Recalculate total amount
        total_tax = sum(Decimal(str(tax.get("amount", 0))) for tax in new_tax_details)
        buy_charge_data["total_amount"] = float(new_pretax + total_tax)

        # Transform charge-specific metadata
        buy_charge_data = self._transform_charge_metadata(buy_charge_data)

        # Add buy-side identifiers
        buy_charge_data["charge_id"] = f"{sell_charge_data.get('charge_id', '')}_buy"
        buy_charge_data["original_sell_charge_id"] = sell_charge_data.get("charge_id")

        return buy_charge_data

    def _transform_amounts(
        self, pretax_amount: Decimal, tax_details: List[Dict[str, Any]]
    ) -> tuple:
        # STUB IMPLEMENTATION - Replace with actual business logic

        # Example transformation logic (to be replaced):
        # 1. Apply buy-side pricing rules
        # 2. Recalculate taxes based on buy-side tax determiners
        # 3. Apply any reseller-specific adjustments

        # For now, return the same amounts (stub)
        new_pretax = pretax_amount
        new_tax_details = copy.deepcopy(tax_details)

        # TODO: Implement actual transformation logic here
        # Examples of what might be implemented:
        # - Apply different tax rates for buy-side
        # - Apply reseller discounts or markups
        # - Convert currencies if needed
        # - Apply different pricing tiers

        return new_pretax, new_tax_details

    def _transform_charge_metadata(self, charge_data: Dict[str, Any]) -> Dict[str, Any]:
        # STUB IMPLEMENTATION - Replace with actual business logic

        # Transform common metadata
        transformed_data = self._transform_common_metadata(charge_data)

        # TODO: Implement charge-specific metadata transformations
        # Examples of what might be implemented:
        # - Transform item categories for buy-side
        # - Update seller information
        # - Modify inclusion charge flags
        # - Update revenue center mappings

        return transformed_data

    def _validate_charge_data(self, charge_data: Dict[str, Any]) -> bool:
        required_fields = ["charge_id", "pretax_amount"]

        for field in required_fields:
            if field not in charge_data:
                return False

        # Validate amount is numeric
        try:
            Decimal(str(charge_data["pretax_amount"]))
        except (ValueError, TypeError):
            return False

        return True
