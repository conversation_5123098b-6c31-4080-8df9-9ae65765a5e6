from typing import Dict, Optional

from finance_erp.domain.financial_transactions.constants import FinancialEventTypes
from finance_erp.domain.financial_transactions.services.reseller_transformers.allowance_transformer import (
    AllowanceResellerTransformer,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.base_transformer import (
    ResellerTransformer,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.charge_transformer import (
    ChargeResellerTransformer,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.payment_transformer import (
    PaymentResellerTransformer,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        ChargeResellerTransformer,
        PaymentResellerTransformer,
        AllowanceResellerTransformer,
    ]
)
class ResellerTransformerFactory:
    def __init__(
        self,
        charge_transformer: ChargeResellerTransformer,
        payment_transformer: PaymentResellerTransformer,
        allowance_transformer: AllowanceResellerTransformer,
    ):
        self._transformers: Dict[str, ResellerTransformer] = {
            FinancialEventTypes.CRS_CHARGE: charge_transformer,
            FinancialEventTypes.CRS_PAYMENT: payment_transformer,
            FinancialEventTypes.CRS_ALLOWANCE: allowance_transformer,
        }

    def get_transformer(self, event_type: str) -> Optional[ResellerTransformer]:
        return self._transformers.get(event_type)

    def supports_event_type(self, event_type: str) -> bool:
        return event_type in self._transformers

    def get_supported_event_types(self) -> list:
        return list(self._transformers.keys())

    def register_transformer(self, event_type: str, transformer: ResellerTransformer):
        self._transformers[event_type] = transformer

    def unregister_transformer(self, event_type: str):
        if event_type in self._transformers:
            del self._transformers[event_type]


class TransformerFactoryError(Exception):
    def __init__(self, message: str, event_type: str = None):
        self.message = message
        self.event_type = event_type
        super().__init__(
            f"Transformer factory error for event type '{event_type}': {message}"
        )
