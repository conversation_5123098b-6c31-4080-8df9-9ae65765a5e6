import copy
from decimal import Decimal
from typing import Any, Dict, List

from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.base_transformer import (
    ResellerTransformer,
    TransformationError,
)
from object_registry import register_instance


@register_instance()
class AllowanceResellerTransformer(ResellerTransformer):
    def transform_to_buy_side(
        self, sell_event: FinancialEventEntity
    ) -> FinancialEventEntity:
        try:
            # Create base buy-side event
            buy_side_event = self._create_base_buy_side_event(sell_event)

            # Transform allowance-specific data
            buy_side_event.event_data = self._transform_allowance_data(
                sell_event.event_data
            )

            return buy_side_event

        except Exception as e:
            raise TransformationError(
                message=str(e),
                sell_event_id=sell_event.financial_event_id,
                transformation_type="allowance",
            )

    def _transform_allowance_data(
        self, sell_allowance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        # Deep copy to avoid modifying original data
        buy_allowance_data = copy.deepcopy(sell_allowance_data)

        # Transform amounts (pretax and tax components)
        original_pretax = Decimal(str(sell_allowance_data.get("pretax_amount", 0)))
        original_tax_details = sell_allowance_data.get("tax_details", [])

        new_pretax, new_tax_details = self._transform_amounts(
            original_pretax, original_tax_details
        )

        # Update transformed amounts
        buy_allowance_data["pretax_amount"] = float(new_pretax)
        buy_allowance_data["tax_details"] = new_tax_details

        # Recalculate total amount
        total_tax = sum(Decimal(str(tax.get("amount", 0))) for tax in new_tax_details)
        buy_allowance_data["total_amount"] = float(new_pretax + total_tax)

        # Transform allowance-specific metadata
        buy_allowance_data = self._transform_allowance_metadata(buy_allowance_data)

        # Add buy-side identifiers
        buy_allowance_data[
            "allowance_id"
        ] = f"{sell_allowance_data.get('allowance_id', '')}_buy"
        buy_allowance_data["original_sell_allowance_id"] = sell_allowance_data.get(
            "allowance_id"
        )

        # Preserve relationship to original charge
        if "charge_id" in sell_allowance_data:
            buy_allowance_data["original_charge_id"] = sell_allowance_data["charge_id"]
            buy_allowance_data["charge_id"] = f"{sell_allowance_data['charge_id']}_buy"

        return buy_allowance_data

    def _transform_amounts(
        self, pretax_amount: Decimal, tax_details: List[Dict[str, Any]]
    ) -> tuple:
        # STUB IMPLEMENTATION - Replace with actual business logic

        # Example transformation logic (to be replaced):
        # 1. Apply buy-side allowance calculation rules
        # 2. Recalculate taxes based on buy-side tax determiners
        # 3. Apply any reseller-specific allowance adjustments
        # 4. Consider different allowance policies for buy-side

        # For now, return the same amounts (stub)
        new_pretax = pretax_amount
        new_tax_details = copy.deepcopy(tax_details)

        # TODO: Implement actual transformation logic here
        # Examples of what might be implemented:
        # - Apply different tax rates for buy-side allowances
        # - Apply reseller-specific allowance percentages
        # - Convert currencies if needed
        # - Apply different allowance caps or limits
        # - Transform allowance types (e.g., percentage vs fixed amount)

        return new_pretax, new_tax_details

    def _transform_allowance_metadata(
        self, allowance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        # STUB IMPLEMENTATION - Replace with actual business logic

        # Transform common metadata
        transformed_data = self._transform_common_metadata(allowance_data)

        # TODO: Implement allowance-specific metadata transformations
        # Examples of what might be implemented:
        # - Transform allowance categories for buy-side
        # - Update allowance reason codes
        # - Modify allowance approval workflows
        # - Update revenue impact calculations
        # - Transform allowance reporting categories

        return transformed_data

    def _transform_allowance_type(self, allowance_type: str) -> str:
        # TODO: Implement allowance type transformation logic
        # Examples:
        # - Map sell-side allowance types to buy-side equivalents
        # - Apply different allowance categorization
        # - Transform promotional allowances

        return allowance_type

    def _transform_allowance_reason(
        self, reason_code: str, reason_description: str
    ) -> tuple:
        # TODO: Implement allowance reason transformation logic
        # Examples:
        # - Map sell-side reason codes to buy-side equivalents
        # - Update reason descriptions for buy-side context
        # - Apply different approval workflows

        return reason_code, reason_description

    def _calculate_buy_side_allowance_impact(
        self, allowance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        # TODO: Implement buy-side allowance impact calculation
        # Examples:
        # - Calculate different revenue impact for buy-side
        # - Apply different cost allocation methods
        # - Update profitability calculations

        return {
            "revenue_impact": allowance_data.get("total_amount", 0),
            "cost_impact": 0,  # Stub value
            "margin_impact": 0,  # Stub value
        }

    def _validate_allowance_data(self, allowance_data: Dict[str, Any]) -> bool:
        required_fields = ["allowance_id", "pretax_amount"]

        for field in required_fields:
            if field not in allowance_data:
                return False

        # Validate amount is numeric
        try:
            Decimal(str(allowance_data["pretax_amount"]))
        except (ValueError, TypeError):
            return False

        return True
