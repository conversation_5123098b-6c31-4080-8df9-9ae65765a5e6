from abc import ABC, abstractmethod
from typing import Any, Dict

from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)


class ResellerTransformer(ABC):
    @abstractmethod
    def transform_to_buy_side(
        self, sell_event: FinancialEventEntity
    ) -> FinancialEventEntity:
        pass

    def _create_base_buy_side_event(
        self, sell_event: FinancialEventEntity
    ) -> FinancialEventEntity:
        # Generate buy-side event ID
        buy_side_event_id = f"{sell_event.financial_event_id}_buy"

        # Create base buy-side event
        buy_side_event = FinancialEventEntity(
            financial_event_id=buy_side_event_id,
            parent_event_id=sell_event.parent_event_id,
            source_system=sell_event.source_system,
            entity_type=sell_event.entity_type,
            entity_id=f"{sell_event.entity_id}_buy",
            bill_id=sell_event.bill_id,
            hotel_id=sell_event.hotel_id,
            posting_date=sell_event.posting_date,
            event_data={},  # Will be set by specific transformers
            processing_status=ProcessingStatus.PENDING,
            system_scope=sell_event.system_scope,
            source_system_metadata=self._create_buy_side_metadata(sell_event),
            related_charge_id=sell_event.related_charge_id,
            related_charge_split_id=sell_event.related_charge_split_id,
        )

        return buy_side_event

    def _create_buy_side_metadata(
        self, sell_event: FinancialEventEntity
    ) -> Dict[str, Any]:
        # Copy original metadata
        buy_side_metadata = (sell_event.source_system_metadata or {}).copy()

        # Add buy-side specific flags
        buy_side_metadata.update(
            {
                "reseller_folio_transaction": True,
                "original_sell_event_id": sell_event.financial_event_id,
                "transaction_side": "buy",
                "generated_from_reseller": True,
            }
        )

        return buy_side_metadata

    def _transform_common_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        # Stub implementation - can be overridden by specific transformers
        transformed_metadata = metadata.copy()

        # Add any common transformations here
        # For example: transform seller information, booking references, etc.

        return transformed_metadata


class TransformationError(Exception):
    def __init__(self, message: str, sell_event_id: str, transformation_type: str):
        self.message = message
        self.sell_event_id = sell_event_id
        self.transformation_type = transformation_type
        super().__init__(
            f"Transformation failed for {transformation_type} event {sell_event_id}: {message}"
        )
