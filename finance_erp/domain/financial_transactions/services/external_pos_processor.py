from decimal import Decimal
from typing import Dict, List, Optional

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.constants import GLRevenueTypes
from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.back_office.entity.ledger_controls import ErrorFileItem
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.financial_transactions.constants import TransactionSide
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    TransactionEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)
from finance_erp.domain.pos.entity.pos_revenue import POSRevenueItem
from object_registry import register_instance


@register_instance()
class ExternalPosItemProcessor:
    def __init__(self):
        pass

    def process(
        self,
        pos_items: List[POSRevenueItem],
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:
        """Process POS items and generate transactions"""
        result = TransactionProcessingResult.empty()

        for item in pos_items:
            # Generate charge transactions (main amount + taxes)
            charge_result = self._generate_charge_transactions(item, config_master)
            result.extend_with(charge_result)

            # Generate payment transactions
            payment_result = self._generate_payment_transactions(item, config_master)
            result.extend_with(payment_result)

        return result

    def _generate_charge_transactions(
        self,
        item: POSRevenueItem,
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:
        result = TransactionProcessingResult.empty()

        for accountable_entity in config_master.get_enabled_entities_for_accounting():
            # Generate main charge transaction
            txn_type = (
                GLRevenueTypes.CHARGE
                if not item.is_allowance
                else GLRevenueTypes.ALLOWANCE
            )

            txn_master = self._get_txn_master(
                item.sku_id,
                item.revenue_center,
                txn_type,
                config_master,
                accountable_entity,
            )

            if txn_master:
                result.append_transaction(
                    self._create_charge_transaction(
                        item,
                        txn_master,
                        accountable_entity,
                        item.pretax_amount,
                    )
                )

            if not txn_master or not txn_master.gl_code:
                result.append_error(
                    self._capture_charge_error(
                        item,
                        accountable_entity,
                        txn_type,
                        txn_master=txn_master,
                    )
                )

            # Generate tax transactions
            for tax in item.tax_details or []:
                tax_amount = float(tax.get("tax_amount", 0))
                if not tax_amount:
                    continue

                tax_txn_type = (
                    GLRevenueTypes.TAX_ON_CHARGE
                    if not item.is_allowance
                    else GLRevenueTypes.TAX_ON_ALLOWANCE
                )

                tax_txn_master = self._get_txn_master(
                    item.sku_id,
                    item.revenue_center,
                    tax_txn_type,
                    config_master,
                    accountable_entity,
                    tax=tax,
                )

                if tax_txn_master:
                    result.append_transaction(
                        self._create_charge_transaction(
                            item,
                            tax_txn_master,
                            accountable_entity,
                            tax_amount,
                            tax_component=tax,
                        )
                    )

                if not tax_txn_master or not tax_txn_master.gl_code:
                    result.append_error(
                        self._capture_tax_error(
                            item,
                            accountable_entity,
                            tax_txn_type,
                            tax,
                            txn_master=tax_txn_master,
                        )
                    )

        return result

    def _generate_payment_transactions(
        self,
        item: POSRevenueItem,
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:
        result = TransactionProcessingResult.empty()

        for accountable_entity in config_master.get_enabled_entities_for_accounting():
            txn_type = (
                GLRevenueTypes.PAYMENT
                if not item.is_allowance
                else GLRevenueTypes.REFUND
            )

            txn_master = self._get_txn_master(
                item.payment_method,
                item.revenue_center,
                txn_type,
                config_master,
                accountable_entity,
                payment_sub_type=item.payment_mode_sub_type,
            )

            if txn_master:
                result.append_transaction(
                    self._create_payment_transaction(
                        item,
                        txn_master,
                        accountable_entity,
                    )
                )

            if not txn_master or not txn_master.gl_code:
                result.append_error(
                    self._capture_payment_error(
                        item,
                        accountable_entity,
                        txn_type,
                        txn_master=txn_master,
                    )
                )

        return result

    @staticmethod
    def _create_charge_transaction(
        item: POSRevenueItem,
        txn_master: TransactionMasterEntity,
        accountable_entity: str,
        amount: float,
        tax_component: Optional[Dict] = None,
    ) -> TransactionEntity:

        if tax_component:
            source_entity_suffix = "_tax"
            tax_metadata = {
                "tax_type": tax_component.get("tax_code"),
                "tax_percentage": tax_component.get("tax_value"),
            }
        else:
            source_entity_suffix = ""
            tax_metadata = {}

        metadata = {
            "sku_id": item.sku_id,
            "sku_category": item.sku_category,
            "revenue_center": item.revenue_center,
            "interface_id": item.interface_id,
            "interface_name": item.interface_name,
            **tax_metadata,
        }

        return TransactionEntity(
            transaction_id=fin_erp_random_id_generator("RTX", max_length=20),
            financial_event_id=item.uu_id,  # POS items don't have financial events
            source_system="pos",
            source_entity_id=f"{item.uu_id}{source_entity_suffix}",
            hotel_id=item.hotel_id,
            posting_date=item.pos_bill_date,
            triggering_source_type="pos_charge",  # POS charge transaction
            document_number=item.bill_id,
            txn_code=txn_master.txn_code,
            gl_code=txn_master.gl_code,
            erp_id=txn_master.erp_name,
            operational_unit=txn_master.operational_unit,
            source=txn_master.profit_center,
            txn_amount=Decimal(str(amount)),
            particular=txn_master.particulars,
            txn_group=txn_master.transaction_type,
            is_mergeable=txn_master.merge_gl_entries,
            metadata=metadata,
            accountable_entity_type=accountable_entity,
            txn_side=TransactionSide.SELL,
        )

    @staticmethod
    def _create_payment_transaction(
        item: POSRevenueItem,
        txn_master: TransactionMasterEntity,
        accountable_entity: str,
    ) -> TransactionEntity:
        """Create payment transaction entity"""

        # Payment amount is negative for accounting (money coming in)
        amount = -Decimal(str(item.amount))

        metadata = {
            "payment_method": item.payment_method,
            "payment_mode_sub_type": item.payment_mode_sub_type,
            "revenue_center": item.revenue_center,
            "interface_id": item.interface_id,
            "interface_name": item.interface_name,
            "guest_name": item.guest_name,
            "reservation_id": item.reservation_id,
        }

        return TransactionEntity(
            transaction_id=fin_erp_random_id_generator("RTX", max_length=20),
            financial_event_id=item.uu_id,  # POS items don't have financial events
            source_system="pos",
            source_entity_id=f"{item.uu_id}_payment",
            hotel_id=item.hotel_id,
            posting_date=item.pos_bill_date,
            triggering_source_type="pos_payment",  # POS payment transaction
            document_number=item.bill_id,
            txn_code=txn_master.txn_code,
            gl_code=txn_master.gl_code,
            erp_id=txn_master.erp_name,
            operational_unit=txn_master.operational_unit,
            source=txn_master.profit_center,
            txn_amount=amount,
            particular=txn_master.particulars,
            txn_group=txn_master.transaction_type,
            is_mergeable=txn_master.merge_gl_entries,
            metadata=metadata,
            accountable_entity_type=accountable_entity,
            txn_side=TransactionSide.SELL,
        )

    @staticmethod
    def _capture_charge_error(
        item: POSRevenueItem,
        accountable_entity: str,
        txn_type: str,
        txn_master: Optional[TransactionMasterEntity] = None,
    ) -> ErrorFileItem:
        """Capture charge transaction error"""

        if not txn_master:
            issue_type = f"Skipped txn {txn_type} {item.sku_category}"
            reason = "Due to missing Txn Master"
        else:
            issue_type = f"Txn Generated without GLCode {txn_type} {item.sku_category}"
            reason = f"GL code was not found for txn master {txn_master.txn_code}"

        other_details = {
            "uu_id": item.uu_id,
            "amount": item.pretax_amount,
            "source_system": "pos",
            "bill_id": item.bill_id,
            "sku_id": item.sku_id,
            "revenue_center": item.revenue_center,
        }

        return ErrorFileItem(
            issue_type=issue_type,
            identifier=item.sku_id or item.sku_category,
            identifier_type="sku_id",
            reason=reason,
            other_details=str(other_details),
            txn_type=txn_type,
            accountable_entity=accountable_entity,
            txn_side=TransactionSide.SELL,
            event_id=item.uu_id,
        )

    @staticmethod
    def _capture_tax_error(
        item: POSRevenueItem,
        accountable_entity: str,
        txn_type: str,
        tax: Dict,
        txn_master: Optional[TransactionMasterEntity] = None,
    ) -> ErrorFileItem:
        """Capture tax transaction error"""

        if not txn_master:
            issue_type = f"Skipped txn {txn_type} {item.sku_category}"
            reason = "Due to missing Txn Master"
        else:
            issue_type = f"Txn Generated without GLCode {txn_type} {item.sku_category}"
            reason = f"GL code was not found for txn master {txn_master.txn_code}"

        other_details = {
            "uu_id": item.uu_id,
            "tax_amount": tax.get("tax_amount"),
            "tax_type": tax.get("tax_code"),
            "tax_percentage": tax.get("tax_value"),
            "source_system": "pos",
            "bill_id": item.bill_id,
            "sku_id": item.sku_id,
            "revenue_center": item.revenue_center,
        }

        return ErrorFileItem(
            issue_type=issue_type,
            identifier=item.sku_id or item.sku_category,
            identifier_type="sku_id",
            reason=reason,
            other_details=str(other_details),
            txn_type=txn_type,
            accountable_entity=accountable_entity,
            txn_side=TransactionSide.SELL,
            event_id=item.uu_id,
        )

    @staticmethod
    def _capture_payment_error(
        item: POSRevenueItem,
        accountable_entity: str,
        txn_type: str,
        txn_master: Optional[TransactionMasterEntity] = None,
    ) -> ErrorFileItem:
        """Capture payment transaction error"""

        if not txn_master:
            issue_type = f"Skipped txn {txn_type} {item.payment_method}"
            reason = "Due to missing Txn Master"
        else:
            issue_type = (
                f"Txn Generated without GLCode {txn_type} {item.payment_method}"
            )
            reason = f"GL code was not found for txn master {txn_master.txn_code}"

        identifier = str(item.payment_method)
        if item.payment_mode_sub_type:
            identifier += f" ({item.payment_mode_sub_type})"

        other_details = {
            "uu_id": item.uu_id,
            "amount": item.amount,
            "source_system": "pos",
            "bill_id": item.bill_id,
            "payment_method": item.payment_method,
            "payment_mode_sub_type": item.payment_mode_sub_type,
            "revenue_center": item.revenue_center,
        }

        return ErrorFileItem(
            issue_type=issue_type,
            identifier=identifier,
            identifier_type="payment_mode",
            reason=reason,
            other_details=str(other_details),
            txn_type=txn_type,
            accountable_entity=accountable_entity,
            txn_side=TransactionSide.SELL,
            event_id=item.uu_id,
        )

    @staticmethod
    def _get_txn_master(
        identifier: str,
        revenue_center: str,
        txn_type: str,
        config_master: ConfigMaster,
        accountable_entity: str,
        payment_sub_type: Optional[str] = None,
        tax: Optional[Dict] = None,
    ) -> Optional[TransactionMasterEntity]:
        """Get transaction master for POS item"""
        try:
            return config_master.get_txn_master_v2(
                txn_type=txn_type,
                identifier=identifier,
                revenue_center=revenue_center,
                tax_percentage=tax.get("tax_value") if tax else None,
                tax_type=tax.get("tax_code") if tax else None,
                payment_sub_type=payment_sub_type,
                accountable_entity=accountable_entity,
                txn_side=TransactionSide.SELL,
            )
        except KeyError:
            return None
