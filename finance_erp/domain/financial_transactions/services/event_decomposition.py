import copy
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List

from finance_erp.domain.financial_transactions.constants import (
    FinancialEventTypes,
    SourceSystemStatus,
)
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
    SourceSystemEventEntity,
)
from finance_erp.domain.financial_transactions.services.financial_event_id_generator import (
    FinancialEventIdGenerator,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


class AbstractEventDecomposer(ABC):
    @abstractmethod
    def get_supported_event_types(self) -> List[str]:
        pass

    @abstractmethod
    def decompose_event(self, source_event) -> List:
        pass

    @staticmethod
    def get_normalise_source_system_status(data) -> SourceSystemStatus:
        status = data.get("status")
        if status in ["posted", "consumed"]:
            return SourceSystemStatus.POSTED
        return SourceSystemStatus.CREATED


@register_instance(dependencies=[])
class CRSBillEventDecomposer(AbstractEventDecomposer):
    def get_supported_event_types(self) -> List[str]:
        return ["bill"]

    def decompose_event(
        self, source_event: SourceSystemEventEntity
    ) -> List[FinancialEventEntity]:
        try:
            event_payload = source_event.event_payload
            financial_events = []

            # Extract charge events
            charge_events = self._extract_charge_events(source_event, event_payload)
            financial_events.extend(charge_events)

            # Extract payment events
            payment_events = self._extract_payment_events(source_event, event_payload)
            financial_events.extend(payment_events)

            # Extract allowance events (from charge splits)
            allowance_events = self._extract_allowance_events(
                source_event, event_payload
            )
            financial_events.extend(allowance_events)

            logger.info(
                "Decomposed CRS bill event %s into %d financial events",
                source_event.entity_id,
                len(financial_events),
            )

            return financial_events

        except Exception as e:
            logger.exception(
                "Failed to decompose CRS bill event %s: %s",
                source_event.entity_id,
                str(e),
            )
            raise

    def _extract_charge_events(
        self, source_event: SourceSystemEventEntity, event_payload: Dict[str, Any]
    ) -> List[FinancialEventEntity]:
        charge_events = []
        charges = event_payload.get("charges", [])

        for charge in charges:
            if not self.is_eligible(charge):
                continue

            event_data = copy.deepcopy(charge)
            event_data.pop("charge_splits", None)

            uu_id = FinancialEventIdGenerator.generate_id(
                FinancialEventTypes.CRS_CHARGE,
                bill_id=event_payload.get("bill_id"),
                charge_id=charge.get("charge_id"),
            )
            financial_event = FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.CRS_CHARGE,
                entity_id=charge.get("charge_id"),
                document_number=event_payload.get("bill_id"),
                hotel_id=source_event.hotel_id,
                source_system_status=self.get_normalise_source_system_status(charge),
                posting_date=charge.get("posting_date"),
                event_data=event_data,
                system_scope=source_event.system_scope,
                source_system_metadata={
                    "parent_info": event_payload.get("parent_info"),
                    "vendor_details": event_payload.get("vendor_details"),
                },
            )
            charge_events.append(financial_event)

        return charge_events

    def _extract_payment_events(
        self, source_event: SourceSystemEventEntity, event_payload: Dict[str, Any]
    ) -> List[FinancialEventEntity]:
        payment_events = []
        payments = event_payload.get("payments", [])

        for payment in payments:
            if not self.is_eligible(payment):
                continue

            event_data = copy.deepcopy(payment)
            event_data.pop("payment_splits", None)

            uu_id = FinancialEventIdGenerator.generate_id(
                FinancialEventTypes.CRS_PAYMENT,
                bill_id=event_payload.get("bill_id"),
                payment_id=payment.get("payment_id"),
            )
            financial_event = FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.CRS_PAYMENT,
                entity_id=payment.get("payment_id"),
                document_number=event_payload.get("bill_id"),
                hotel_id=source_event.hotel_id,
                source_system_status=self.get_normalise_source_system_status(payment),
                posting_date=payment.get("posting_date"),
                event_data=event_data,
                system_scope=source_event.system_scope,
                source_system_metadata={
                    "parent_info": event_payload.get("parent_info"),
                    "vendor_details": event_payload.get("vendor_details"),
                },
            )
            payment_events.append(financial_event)

        return payment_events

    def _extract_allowance_events(
        self, source_event: SourceSystemEventEntity, event_payload: Dict[str, Any]
    ) -> List[FinancialEventEntity]:
        allowance_events = []
        charges = event_payload.get("charges", [])

        for charge in charges:
            charge_splits = charge.get("charge_splits", [])

            for split in charge_splits:
                for allowance in split.get("allowances", []):
                    if not self.is_eligible(split):
                        continue

                    uu_id = FinancialEventIdGenerator.generate_id(
                        FinancialEventTypes.CRS_ALLOWANCE,
                        bill_id=event_payload.get("bill_id"),
                        charge_id=charge.get("charge_id"),
                        charge_split_id=split.get("charge_split_id"),
                        allowance_id=allowance.get("allowance_id"),
                    )

                    financial_event = FinancialEventEntity(
                        financial_event_id=uu_id,
                        parent_event_id=source_event.event_id,
                        source_system=source_event.source_system,
                        entity_type=FinancialEventTypes.CRS_ALLOWANCE,
                        entity_id=allowance.get("allowance_id"),
                        document_number=event_payload.get("bill_id"),
                        hotel_id=source_event.hotel_id,
                        posting_date=charge.get("posting_date"),
                        event_data=allowance,
                        source_system_status=self.get_normalise_source_system_status(
                            allowance
                        ),
                        system_scope=source_event.system_scope,
                        related_charge_id=charge.get("charge_id"),
                        related_charge_split_id=split.get("charge_split_id"),
                        source_system_metadata={
                            "charge": charge,
                            "parent_info": event_payload.get("parent_info"),
                            "vendor_details": event_payload.get("vendor_details"),
                        },
                    )
                    allowance_events.append(financial_event)

        return allowance_events

    @staticmethod
    def is_eligible(data) -> bool:
        return True


@register_instance(dependencies=[])
class CRSInvoiceEventDecomposer(AbstractEventDecomposer):
    def get_supported_event_types(self) -> List[str]:
        return ["bill"]

    def decompose_event(
        self, source_event: SourceSystemEventEntity
    ) -> List[FinancialEventEntity]:
        try:
            event_payload = source_event.event_payload

            # Check if this is an invoice-enhanced bill event
            invoice_context_data = event_payload.get("_invoice_context")
            if not invoice_context_data:
                # This is a regular bill event, not an invoice event - return empty
                return []

            financial_events = []

            # Extract invoiced charge events (from charge splits)
            invoiced_charge_events = self._extract_invoiced_charge_events(
                source_event, event_payload, invoice_context_data
            )
            financial_events.extend(invoiced_charge_events)

            # Extract invoiced payment events (from payment splits)
            invoiced_payment_events = self._extract_invoiced_payment_events(
                source_event, event_payload, invoice_context_data
            )
            financial_events.extend(invoiced_payment_events)

            # Extract invoiced allowance events (from existing allowances in splits)
            invoiced_allowance_events = self._extract_invoiced_allowance_events(
                source_event, event_payload, invoice_context_data
            )
            financial_events.extend(invoiced_allowance_events)

            logger.info(
                "Decomposed CRS invoice event %s into %d financial events for invoice %s",
                source_event.entity_id,
                len(financial_events),
                invoice_context_data.get("invoice_id"),
            )

            return financial_events

        except Exception as e:
            logger.exception(
                "Failed to decompose CRS invoice event %s: %s",
                source_event.entity_id,
                str(e),
            )
            raise

    def _extract_invoiced_charge_events(
        self,
        source_event: SourceSystemEventEntity,
        event_payload: Dict[str, Any],
        invoice_context_data: Dict[str, Any],
    ) -> List[FinancialEventEntity]:
        invoiced_charge_events = []
        charges = event_payload.get("charges", [])
        target_billed_entity_account = invoice_context_data.get(
            "target_billed_entity_account", {}
        )
        invoice_id = invoice_context_data.get("invoice_id")
        posting_date = invoice_context_data.get("invoice_context", {}).get(
            "invoice_date"
        )

        for charge in charges:
            if not self.is_eligible(charge):
                continue

            charge_splits = charge.get("charge_splits", [])

            for split in charge_splits:
                # Filter by billed entity account
                split_billed_entity_account = split.get("billed_entity_account", {})
                if not self._matches_target_billed_entity_account(
                    split_billed_entity_account, target_billed_entity_account
                ):
                    continue

                # Generate ID for invoiced charge split
                uu_id = FinancialEventIdGenerator.generate_id(
                    FinancialEventTypes.CRS_INVOICED_CHARGE,
                    bill_id=event_payload.get("bill_id"),
                    charge_id=charge.get("charge_id"),
                    charge_split_id=split.get("charge_split_id"),
                    invoice_id=invoice_id,
                )

                # Create hybrid event data (split amounts + charge metadata)
                event_data = self._create_hybrid_charge_data(charge, split)

                financial_event = FinancialEventEntity(
                    financial_event_id=uu_id,
                    parent_event_id=source_event.event_id,
                    source_system=source_event.source_system,
                    entity_type=FinancialEventTypes.CRS_INVOICED_CHARGE,
                    entity_id=split.get("charge_split_id"),
                    document_number=invoice_id,
                    hotel_id=source_event.hotel_id,
                    posting_date=posting_date,
                    event_data=event_data,
                    source_system_status=SourceSystemStatus.POSTED,
                    system_scope=source_event.system_scope,
                    related_charge_id=charge.get("charge_id"),
                    related_charge_split_id=split.get("charge_split_id"),
                    source_system_metadata=self._build_invoice_metadata(
                        charge, event_payload, invoice_context_data
                    ),
                )
                invoiced_charge_events.append(financial_event)

        return invoiced_charge_events

    def _extract_invoiced_payment_events(
        self,
        source_event: SourceSystemEventEntity,
        event_payload: Dict[str, Any],
        invoice_context_data: Dict[str, Any],
    ) -> List[FinancialEventEntity]:
        invoiced_payment_events = []
        payments = event_payload.get("payments", [])
        target_billed_entity_account = invoice_context_data.get(
            "target_billed_entity_account", {}
        )
        invoice_id = invoice_context_data.get("invoice_id")
        posting_date = invoice_context_data.get("invoice_context", {}).get(
            "invoice_date"
        )

        for payment in payments:
            if not self.is_eligible(payment):
                continue

            payment_splits = payment.get("payment_splits", [])

            for split in payment_splits:
                # Filter by billed entity account
                split_billed_entity_account = split.get("billed_entity_account", {})
                if not self._matches_target_billed_entity_account(
                    split_billed_entity_account, target_billed_entity_account
                ):
                    continue

                # Generate ID for invoiced payment split
                uu_id = FinancialEventIdGenerator.generate_id(
                    FinancialEventTypes.CRS_INVOICED_PAYMENT,
                    bill_id=event_payload.get("bill_id"),
                    payment_id=payment.get("payment_id"),
                    payment_split_id=split.get("payment_split_id"),
                    invoice_id=invoice_id,
                )

                # Create hybrid event data (split amounts + payment metadata)
                event_data = self._create_hybrid_payment_data(payment, split)

                financial_event = FinancialEventEntity(
                    financial_event_id=uu_id,
                    parent_event_id=source_event.event_id,
                    source_system=source_event.source_system,
                    entity_type=FinancialEventTypes.CRS_INVOICED_PAYMENT,
                    entity_id=split.get("payment_split_id"),
                    document_number=invoice_id,
                    hotel_id=source_event.hotel_id,
                    posting_date=posting_date,
                    event_data=event_data,
                    source_system_status=SourceSystemStatus.POSTED,
                    system_scope=source_event.system_scope,
                    source_system_metadata=self._build_invoice_metadata(
                        payment, event_payload, invoice_context_data
                    ),
                )
                invoiced_payment_events.append(financial_event)

        return invoiced_payment_events

    def _extract_invoiced_allowance_events(
        self,
        source_event: SourceSystemEventEntity,
        event_payload: Dict[str, Any],
        invoice_context_data: Dict[str, Any],
    ) -> List[FinancialEventEntity]:
        invoiced_allowance_events = []
        charges = event_payload.get("charges", [])
        target_billed_entity_account = invoice_context_data.get(
            "target_billed_entity_account", {}
        )
        invoice_id = invoice_context_data.get("invoice_id")
        posting_date = invoice_context_data.get("invoice_context", {}).get(
            "invoice_date"
        )

        for charge in charges:
            charge_splits = charge.get("charge_splits", [])

            for split in charge_splits:
                # Filter by billed entity account
                split_billed_entity_account = split.get("billed_entity_account", {})
                if not self._matches_target_billed_entity_account(
                    split_billed_entity_account, target_billed_entity_account
                ):
                    continue

                for allowance in split.get("allowances", []):
                    if not self.is_eligible(split):
                        continue

                    uu_id = FinancialEventIdGenerator.generate_id(
                        FinancialEventTypes.CRS_INVOICED_ALLOWANCE,
                        bill_id=event_payload.get("bill_id"),
                        charge_id=charge.get("charge_id"),
                        charge_split_id=split.get("charge_split_id"),
                        allowance_id=allowance.get("allowance_id"),
                        invoice_id=invoice_id,
                    )

                    source_system_metadata = self._build_invoice_metadata(
                        charge, event_payload, invoice_context_data
                    )
                    source_system_metadata["charge"] = charge

                    financial_event = FinancialEventEntity(
                        financial_event_id=uu_id,
                        parent_event_id=source_event.event_id,
                        source_system=source_event.source_system,
                        entity_type=FinancialEventTypes.CRS_INVOICED_ALLOWANCE,
                        entity_id=allowance.get("allowance_id"),
                        document_number=invoice_id,
                        hotel_id=source_event.hotel_id,
                        posting_date=posting_date,
                        event_data=allowance,
                        source_system_status=SourceSystemStatus.POSTED,
                        system_scope=source_event.system_scope,
                        related_charge_id=charge.get("charge_id"),
                        related_charge_split_id=split.get("charge_split_id"),
                        source_system_metadata=source_system_metadata,
                    )
                    invoiced_allowance_events.append(financial_event)

        return invoiced_allowance_events

    @staticmethod
    def _matches_target_billed_entity_account(
        split_billed_entity_account: Dict[str, Any],
        target_billed_entity_account: Dict[str, Any],
    ) -> bool:
        target_billed_entity_id = target_billed_entity_account.get("billed_entity_id")
        target_account_number = target_billed_entity_account.get("account_number")

        split_billed_entity_id = split_billed_entity_account.get("billed_entity_id")
        split_account_number = split_billed_entity_account.get("account_number")

        return (
            target_billed_entity_id == split_billed_entity_id
            and target_account_number == split_account_number
        )

    @staticmethod
    def _create_hybrid_charge_data(
        charge: Dict[str, Any], split: Dict[str, Any]
    ) -> Dict[str, Any]:
        # Combine split amounts with charge metadata
        return {
            # Split-level amounts (override charge amounts)
            "pretax_amount": split.get("pre_tax"),
            "tax_amount": split.get("tax"),
            "posttax_amount": split.get("post_tax"),
            "tax_details": split.get("tax_details"),
            "billed_entity_account": split.get("billed_entity_account"),
            # Charge-level metadata
            "item": charge.get("item"),
            "charge_type": charge.get("type"),
            "bill_to_type": charge.get("bill_to_type"),
            "sku_category_id": charge.get("item", {}).get("sku_category_id"),
            "applicable_date": charge.get("applicable_date"),
            "charge_components": charge.get("charge_components"),
            # Split-specific fields
            "charge_split_id": split.get("charge_split_id"),
            "percentage": split.get("percentage"),
            "charge_id": charge.get("charge_id"),
        }

    @staticmethod
    def _create_hybrid_payment_data(
        payment: Dict[str, Any], split: Dict[str, Any]
    ) -> Dict[str, Any]:
        # Combine split amounts with payment metadata
        return {
            # Split-level amounts
            "amount": split.get("amount"),
            "billed_entity_account": split.get("billed_entity_account"),
            # Payment-level metadata
            "payment_mode": payment.get("payment_mode"),
            "payment_type": payment.get("payment_type"),
            "payment_channel": payment.get("payment_channel"),
            "payment_ref_id": payment.get("payment_ref_id"),
            "date_of_payment": payment.get("date_of_payment"),
            "paid_by": payment.get("paid_by"),
            "paid_to": payment.get("paid_to"),
            "confirmed": payment.get("confirmed"),
            # Split-specific fields
            "payment_split_id": split.get("payment_split_id"),
            "payment_id": payment.get("payment_id"),
        }

    @staticmethod
    def _build_invoice_metadata(
        parent_entity: Dict[str, Any],
        event_payload: Dict[str, Any],
        invoice_context_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        invoice_context = invoice_context_data.get("invoice_context", {})
        return {
            "invoice_id": invoice_context_data.get("invoice_id"),
            "invoice_number": invoice_context_data.get("invoice_number"),
            "issued_to_type": invoice_context.get("issued_to_type"),
            "issued_by_type": invoice_context.get("issued_by_type"),
            "bill_to": invoice_context.get("bill_to"),
            "issued_by": invoice_context.get("issued_by"),
            "parent_entity": parent_entity,
            "parent_info": event_payload.get("parent_info"),
            "vendor_details": event_payload.get("vendor_details"),
        }

    @staticmethod
    def is_eligible(data) -> bool:
        return True


@register_instance(dependencies=[])
class POSBillEventDecomposer(AbstractEventDecomposer):
    def get_supported_event_types(self) -> List[str]:
        return ["bill"]

    def decompose_event(
        self, source_event: SourceSystemEventEntity
    ) -> List[FinancialEventEntity]:
        try:
            event_payload = source_event.event_payload
            financial_events = []

            # Extract charge events
            charge_events = self._extract_charge_events(source_event, event_payload)
            financial_events.extend(charge_events)

            # Extract payment events
            payment_events = self._extract_payment_events(source_event, event_payload)
            financial_events.extend(payment_events)

            # Note: POS does not process allowances like CRS
            logger.info(
                "Decomposed POS bill event %s into %d financial events",
                source_event.entity_id,
                len(financial_events),
            )

            return financial_events

        except Exception as e:
            logger.exception(
                "Failed to decompose POS bill event %s: %s",
                source_event.entity_id,
                str(e),
            )
            raise

    def _extract_charge_events(
        self, source_event: SourceSystemEventEntity, event_payload: Dict[str, Any]
    ) -> List[FinancialEventEntity]:
        charge_events = []
        charges = event_payload.get("charges", [])

        for charge in charges:
            if not self.is_eligible(charge):
                continue

            event_data = copy.deepcopy(charge)
            event_data.pop("charge_splits", None)

            uu_id = FinancialEventIdGenerator.generate_id(
                FinancialEventTypes.POS_CHARGE,
                bill_id=event_payload.get("bill_id"),
                charge_id=charge.get("charge_id"),
            )

            pos_seller_id = event_payload.get("vendor_details", {}).get("vendor_id")

            financial_event = FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.POS_CHARGE,
                entity_id=charge.get("charge_id"),
                document_number=event_payload.get("bill_id"),
                hotel_id=source_event.hotel_id,
                posting_date=charge.get("posting_date"),
                source_system_status=charge.get("status"),
                event_data=charge,
                system_scope=source_event.system_scope,
                source_system_metadata={
                    "vendor_details": event_payload.get("vendor_details"),
                    "pos_seller_id": pos_seller_id,
                },
            )
            charge_events.append(financial_event)

        return charge_events

    def _extract_payment_events(
        self, source_event: SourceSystemEventEntity, event_payload: Dict[str, Any]
    ) -> List[FinancialEventEntity]:
        payment_events = []
        payments = event_payload.get("payments", [])

        for payment in payments:
            if not self.is_eligible(payment):
                continue

            event_data = copy.deepcopy(payment)
            event_data.pop("payment_splits", None)

            uu_id = FinancialEventIdGenerator.generate_id(
                FinancialEventTypes.POS_PAYMENT,
                bill_id=event_payload.get("bill_id"),
                payment_id=payment.get("payment_id"),
            )

            pos_seller_id = event_payload.get("vendor_details", {}).get("vendor_id")

            financial_event = FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.POS_PAYMENT,
                entity_id=payment.get("payment_id"),
                document_number=event_payload.get("bill_id"),
                hotel_id=source_event.hotel_id,
                posting_date=payment.get("posting_date"),
                source_system_status=payment.get("status"),
                event_data=payment,
                system_scope=source_event.system_scope,
                source_system_metadata={
                    "vendor_details": event_payload.get("vendor_details"),
                    "pos_seller_id": pos_seller_id,
                },
            )
            payment_events.append(financial_event)

        return payment_events

    @staticmethod
    def is_eligible(data) -> bool:
        return True


@register_instance(dependencies=[])
class AREventDecomposer(AbstractEventDecomposer):
    def get_supported_event_types(self) -> List[str]:
        return ["ar_credit", "ar_debit", "ar_reversal"]

    def decompose_event(
        self, source_event: SourceSystemEventEntity
    ) -> List[FinancialEventEntity]:
        event_payload = source_event.event_payload
        event_type = event_payload.get("event_type")

        if event_type == "ar_credit":
            return self._extract_credit_events(source_event, event_payload)
        elif event_type == "ar_debit":
            return self._extract_debit_events(source_event, event_payload)
        elif event_type == "ar_reversal":
            return self._extract_reversal_events(source_event, event_payload)
        return []

    def _extract_credit_events(self, source_event, event_payload):
        credit_id = event_payload.get("credit_id")
        uu_id = FinancialEventIdGenerator.generate_id(
            FinancialEventTypes.AR_CREDIT, credit_id=credit_id
        )

        return [
            FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.AR_CREDIT,
                entity_id=credit_id,
                document_number=event_payload.get("reference_number"),
                hotel_id=source_event.hotel_id,
                posting_date=event_payload.get("posting_date"),
                source_system_status=event_payload.get("status", "created"),
                event_data=event_payload,
                system_scope=source_event.system_scope,
            )
        ]

    def _extract_debit_events(self, source_event, event_payload):
        debit_id = event_payload.get("debit_id")
        uu_id = FinancialEventIdGenerator.generate_id(
            FinancialEventTypes.AR_DEBIT, debit_id=debit_id
        )

        return [
            FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.AR_DEBIT,
                entity_id=debit_id,
                document_number=event_payload.get("reference_number"),
                hotel_id=source_event.hotel_id,
                posting_date=event_payload.get("posting_date"),
                source_system_status=event_payload.get("status", "created"),
                event_data=event_payload,
                system_scope=source_event.system_scope,
            )
        ]

    def _extract_reversal_events(self, source_event, event_payload):
        reversal_id = event_payload.get("reversal_id")
        uu_id = FinancialEventIdGenerator.generate_id(
            FinancialEventTypes.AR_REVERSAL, reversal_id=reversal_id
        )

        return [
            FinancialEventEntity(
                financial_event_id=uu_id,
                parent_event_id=source_event.event_id,
                source_system=source_event.source_system,
                entity_type=FinancialEventTypes.AR_REVERSAL,
                entity_id=reversal_id,
                document_number=event_payload.get("reference_number"),
                hotel_id=source_event.hotel_id,
                posting_date=event_payload.get("posting_date"),
                source_system_status=event_payload.get("status", "created"),
                event_data=event_payload,
                system_scope=source_event.system_scope,
            )
        ]

    @staticmethod
    def is_eligible(data) -> bool:
        return True
