from finance_erp.domain.financial_transactions.constants import FinancialEventTypes
from finance_erp.domain.financial_transactions.services.event_processor.allowance_processor import (
    AllowanceProcessor,
)
from finance_erp.domain.financial_transactions.services.event_processor.base_processor import (
    BaseProcessor,
)
from finance_erp.domain.financial_transactions.services.event_processor.charge_processor import (
    ChargeProcessor,
)
from finance_erp.domain.financial_transactions.services.event_processor.payment_processor import (
    PaymentProcessor,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        PaymentProcessor,
        ChargeProcessor,
        AllowanceProcessor,
    ]
)
class TransactionProcessorRegistry:
    def __init__(
        self,
        payment_processor: PaymentProcessor,
        charge_processor: ChargeProcessor,
        allowance_processor: AllowanceProcessor,
    ):
        self._register = {
            FinancialEventTypes.CRS_CHARGE: charge_processor,
            FinancialEventTypes.CRS_PAYMENT: payment_processor,
            FinancialEventTypes.CRS_ALLOWANCE: allowance_processor,
            FinancialEventTypes.CRS_INVOICED_CHARGE: charge_processor,
            FinancialEventTypes.CRS_INVOICED_PAYMENT: payment_processor,
            FinancialEventTypes.CRS_INVOICED_ALLOWANCE: allowance_processor,
        }

    def get(self, event_type) -> BaseProcessor:
        return self._register.get(event_type)
