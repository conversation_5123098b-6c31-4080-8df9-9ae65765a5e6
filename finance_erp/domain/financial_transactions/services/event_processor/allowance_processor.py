from typing import Optional

from ths_common.value_objects import TaxDetail
from thsc.crs.convertors.billing_convertors import AllowanceConvertor, ChargeConvertor
from thsc.crs.entities.billing import Allowance, Charge

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.constants import GLRevenueTypes
from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.back_office.entity.ledger_controls import ErrorFileItem
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.financial_transactions.constants import TransactionSide
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    TransactionEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)
from finance_erp.domain.financial_transactions.services.event_processor.base_processor import (
    BaseProcessor,
)
from object_registry import register_instance


@register_instance()
class AllowanceProcessor(BaseProcessor):
    def __init__(self):
        pass

    def process(
        self,
        financial_event: FinancialEventEntity,
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:

        txn_side = TransactionSide.SELL
        if financial_event.source_system_metadata.get("reseller_transaction"):
            txn_side = TransactionSide.BUY

        return self._generate_transactions(
            config_master,
            financial_event,
            txn_side=txn_side,
        )

    def _generate_transactions(
        self,
        config_master,
        financial_event,
        txn_side=TransactionSide.SELL,
    ) -> TransactionProcessingResult:
        result = TransactionProcessingResult.empty()

        allowance_data = financial_event.event_data
        charge_data = financial_event.source_system_metadata.get("charge")

        allowance_entity: Allowance = AllowanceConvertor().from_dict(allowance_data)
        charge_entity: Charge = ChargeConvertor().from_dict(charge_data)

        for accountable_entity in config_master.get_enabled_entities_for_accounting():

            seller_id = (
                financial_event.source_system_seller_id
                or charge_entity.item.details.get("seller_id")
            )

            txn_master = self._get_txn_master(
                accountable_entity,
                charge_entity,
                GLRevenueTypes.ALLOWANCE,
                config_master,
                txn_side,
                seller_id=seller_id,
            )

            if txn_master:
                result.append_transaction(
                    self._create_transactions(
                        financial_event,
                        allowance_entity,
                        txn_master,
                        accountable_entity,
                        txn_side,
                        charge_entity=charge_entity,
                    )
                )

            if not txn_master or not txn_master.gl_code:
                result.append_error(
                    self._capture_errors(
                        financial_event,
                        allowance_entity,
                        charge_entity,
                        accountable_entity,
                        txn_side,
                        txn_master=txn_master,
                    )
                )

            for tax in allowance_entity.tax_details or []:
                tax_amount = tax.amount
                if not tax_amount:
                    continue

                tax_txn_master = self._get_txn_master(
                    accountable_entity,
                    charge_entity,
                    GLRevenueTypes.TAX_ON_ALLOWANCE,
                    config_master,
                    txn_side,
                    tax,
                )

                if tax_txn_master:
                    result.append_transaction(
                        self._create_transactions(
                            financial_event,
                            allowance_entity,
                            tax_txn_master,
                            accountable_entity,
                            txn_side,
                            charge_entity=charge_entity,
                            tax_component=tax,
                        )
                    )

                if not tax_txn_master or not tax_txn_master.gl_code:
                    result.append_error(
                        self._capture_errors(
                            financial_event,
                            allowance_entity,
                            charge_entity,
                            accountable_entity,
                            txn_side,
                            txn_master=tax_txn_master,
                            tax_entry=tax,
                        )
                    )
        return result

    @staticmethod
    def _get_txn_master(
        accountable_entity: str,
        charge_entity: Charge,
        txn_type: str,
        config_master: ConfigMaster,
        txn_side: str,
        tax: Optional[TaxDetail] = None,
        seller_id: Optional[str] = None,
    ) -> Optional[TransactionMasterEntity]:

        item_id = charge_entity.item.item_id
        if not item_id and charge_entity.item.name == "RoomStay":
            item_id = "stay"

        try:
            return config_master.get_txn_master_v2(
                txn_type=txn_type,
                identifier=item_id,
                seller_id=seller_id,
                txn_side=txn_side,
                accountable_entity=accountable_entity,
                tax_percentage=tax and tax.percentage,
                tax_type=tax and tax.tax_type,
            )
        except KeyError:
            return None

    @staticmethod
    def _create_transactions(
        financial_event: FinancialEventEntity,
        allowance_entity: Allowance,
        txn_master: TransactionMasterEntity,
        accountable_entity: str,
        txn_side: str,
        charge_entity: Charge,
        tax_component: Optional[TaxDetail] = None,
    ) -> TransactionEntity:

        if tax_component:
            amount = tax_component.amount.amount
            source_entity_suffix = "_tax"
            is_tax = True
            tax_metadata = {
                "tax_type": tax_component.tax_type,
                "tax_percentage": tax_component.percentage,
            }
        else:
            amount = allowance_entity.pretax_amount.amount
            source_entity_suffix = ""
            is_tax = False
            tax_metadata = {}

        metadata = {
            "is_allowance": True,
            "charge_split_id": financial_event.related_charge_split_id,
            "charge_id": charge_entity.charge_id,
            "bill_id": financial_event.document_number,
            "allowance_id": allowance_entity.allowance_id,
            "category": charge_entity.item.sku_category_id,
            "source_system_metadata": financial_event.source_system_metadata,
            "charge_item_details": dict(
                item_id=charge_entity.item.item_id,
                name=charge_entity.item.name,
                category=charge_entity.item.sku_category_id,
                details=charge_entity.item.details,
            ),
        }
        if is_tax:
            metadata.update({"is_tax": True, **tax_metadata})

        source_entity_id = (
            f"{financial_event.document_number}_{financial_event.related_charge_id}_"
            f"{financial_event.related_charge_split_id}_"
            f"{allowance_entity.allowance_id}{source_entity_suffix}"
        )

        return TransactionEntity(
            transaction_id=fin_erp_random_id_generator("RTX", max_length=20),
            financial_event_id=financial_event.financial_event_id,
            source_system=financial_event.source_system,
            source_entity_id=source_entity_id,
            hotel_id=financial_event.hotel_id,
            posting_date=financial_event.posting_date,
            triggering_source_type=financial_event.entity_type,
            document_number=financial_event.document_number,
            txn_code=txn_master.txn_code,
            gl_code=txn_master.gl_code,
            erp_id=txn_master.erp_name,
            operational_unit=txn_master.operational_unit,
            source=txn_master.profit_center,
            txn_amount=-amount,
            particular=txn_master.particulars,
            txn_group=txn_master.transaction_type,
            is_mergeable=txn_master.merge_gl_entries,
            metadata=metadata,
            accountable_entity_type=accountable_entity,
            txn_side=txn_side,
        )

    @staticmethod
    def _capture_errors(
        financial_event: FinancialEventEntity,
        allowance_entity: Allowance,
        charge_entity: Charge,
        accountable_entity: str,
        txn_side: str,
        txn_master: Optional[TransactionMasterEntity] = None,
        tax_entry: Optional[TaxDetail] = None,
    ) -> ErrorFileItem:
        txn_type = (
            GLRevenueTypes.TAX_ON_ALLOWANCE if tax_entry else GLRevenueTypes.ALLOWANCE
        )
        item_name = charge_entity.item.name

        if not txn_master:
            issue_type = f"Skipped txn {txn_type} {item_name}"
            reason = "Due to missing Txn Master"
        else:
            issue_type = f"Txn Generated without GLCode {txn_type} {item_name}"
            reason = f"GL code was not found for txn master {txn_master.txn_code}"

        amount = tax_entry.amount if tax_entry else allowance_entity.pretax_amount

        other_details = {
            "financial_event_id": financial_event.financial_event_id,
            "amount": amount,
            "source_system": financial_event.source_system,
            "allowance_id": allowance_entity.allowance_id,
            "charge_id": financial_event.related_charge_id,
            "charge_split_id": financial_event.related_charge_split_id,
            "bill_id": financial_event.document_number,
        }

        if tax_entry:
            other_details.update(
                {
                    "tax_type": tax_entry.tax_type,
                    "tax_percentage": tax_entry.percentage,
                }
            )

        return ErrorFileItem(
            issue_type=issue_type,
            identifier=charge_entity.item.item_id or charge_entity.item.name,
            identifier_type="sku_id",
            reason=reason,
            other_details=str(other_details),
            txn_type=txn_type,
            accountable_entity=accountable_entity,
            txn_side=txn_side,
            event_id=financial_event.financial_event_id,
        )
