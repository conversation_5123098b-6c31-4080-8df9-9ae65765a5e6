from typing import Optional

from ths_common.constants.billing_constants import PaymentTypes
from thsc.crs.convertors.billing_convertors import PaymentConvertor
from thsc.crs.entities.billing import Payment

from finance_erp.common.utils.utils import fin_erp_random_id_generator
from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.back_office.entity.ledger_controls import ErrorFileItem
from finance_erp.domain.back_office.entity.transaction_master import (
    TransactionMasterEntity,
)
from finance_erp.domain.financial_transactions.constants import TransactionSide
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    TransactionEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)
from finance_erp.domain.financial_transactions.services.event_processor.base_processor import (
    BaseProcessor,
)
from object_registry import register_instance


@register_instance()
class PaymentProcessor(BaseProcessor):
    def __init__(self):
        pass

    def process(
        self,
        financial_event: FinancialEventEntity,
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:

        txn_side = TransactionSide.SELL
        if financial_event.source_system_metadata.get("reseller_transaction"):
            txn_side = TransactionSide.BUY

        return self._generate_transactions(
            config_master,
            financial_event,
            txn_side=txn_side,
        )

    def _generate_transactions(
        self,
        config_master,
        financial_event,
        txn_side=TransactionSide.SELL,
    ) -> TransactionProcessingResult:
        result = TransactionProcessingResult.empty()

        payment_entity: Payment = PaymentConvertor().from_dict(
            financial_event.event_data
        )

        for accountable_entity in config_master.get_enabled_entities_for_accounting():

            seller_id = financial_event.source_system_seller_id

            txn_master = self._get_txn_master(
                payment_entity,
                config_master,
                accountable_entity,
                txn_side,
                seller_id=seller_id,
            )

            if txn_master:
                result.append_transaction(
                    self._create_transactions(
                        financial_event,
                        payment_entity,
                        txn_master,
                        accountable_entity,
                        txn_side,
                    )
                )

            if not txn_master or not txn_master.gl_code:
                result.append_error(
                    self._capture_errors(
                        financial_event,
                        payment_entity,
                        accountable_entity,
                        txn_side,
                        txn_master=txn_master,
                    )
                )
        return result

    @staticmethod
    def _get_txn_master(
        payment: Payment,
        config_master: ConfigMaster,
        accountable_entity: str,
        txn_side: str,
        seller_id: Optional[str] = None,
    ) -> Optional[TransactionMasterEntity]:

        try:
            return config_master.get_txn_master_v2(
                txn_type=str(payment.payment_type),
                identifier=str(payment.payment_mode),
                seller_id=seller_id,
                txn_side=txn_side,
                payment_sub_type=payment.payment_mode_sub_type,
                accountable_entity=accountable_entity,
            )
        except KeyError:
            return None

    @staticmethod
    def _create_transactions(
        financial_event: FinancialEventEntity,
        payment: Payment,
        txn_master: TransactionMasterEntity,
        accountable_entity: str,
        txn_side: str,
    ) -> TransactionEntity:

        payment_amount = payment.amount.amount
        amount = (
            payment_amount
            if payment.payment_type == PaymentTypes.PAYMENT
            else -payment_amount
        )

        metadata = {
            "payment_mode": payment.payment_mode,
            "payment_mode_sub_type": payment.payment_mode_sub_type,
            "source_system_metadata": financial_event.source_system_metadata,
        }

        return TransactionEntity(
            transaction_id=fin_erp_random_id_generator("RTX", max_length=20),
            financial_event_id=financial_event.financial_event_id,
            source_system=financial_event.source_system,
            source_entity_id=f"{financial_event.document_number}_{payment.payment_id}",
            hotel_id=financial_event.hotel_id,
            posting_date=financial_event.posting_date,
            triggering_source_type=financial_event.entity_type,
            document_number=financial_event.document_number,
            txn_code=txn_master.txn_code,
            gl_code=txn_master.gl_code,
            erp_id=txn_master.erp_name,
            operational_unit=txn_master.operational_unit,
            source=txn_master.profit_center,
            txn_amount=amount,
            particular=txn_master.particulars,
            txn_group=txn_master.transaction_type,
            is_mergeable=txn_master.merge_gl_entries,
            metadata=metadata,
            accountable_entity_type=accountable_entity,
            txn_side=txn_side,
        )

    @staticmethod
    def _capture_errors(
        financial_event: FinancialEventEntity,
        payment_entity: Payment,
        accountable_entity: str,
        txn_side: str,
        txn_master: Optional[TransactionMasterEntity] = None,
    ) -> ErrorFileItem:
        txn_type = payment_entity.payment_type

        if not txn_master:
            issue_type = f"Skipped txn {txn_type} {payment_entity.payment_mode}"
            reason = "Due to missing Txn Master"
        else:
            issue_type = (
                f"Txn Generated without GLCode {txn_type} {payment_entity.payment_mode}"
            )
            reason = f"GL code was not found for txn master {txn_master.txn_code}"

        amount = (
            payment_entity.amount
            if payment_entity.payment_type == "payment"
            else -payment_entity.amount
        )

        other_details = {
            "financial_event_id": financial_event.financial_event_id,
            "amount": amount,
            "source_system": financial_event.source_system,
            "bill_id": financial_event.document_number,
            "payment_id": payment_entity.payment_id,
        }
        identifier = str(payment_entity.payment_mode)
        if payment_entity.payment_mode_sub_type:
            identifier += f" ({payment_entity.payment_mode_sub_type})"

        return ErrorFileItem(
            issue_type=issue_type,
            identifier=identifier,
            identifier_type="payment_mode",
            reason=reason,
            other_details=str(other_details),
            txn_type=txn_type,
            accountable_entity=accountable_entity,
            txn_side=txn_side,
            event_id=financial_event.financial_event_id,
        )
