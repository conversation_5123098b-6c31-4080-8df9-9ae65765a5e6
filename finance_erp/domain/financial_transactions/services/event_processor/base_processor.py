from abc import ABC, abstractmethod

from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)


@abstractmethod
class BaseProcessor(ABC):
    def process(
        self,
        financial_event: FinancialEventEntity,
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:
        raise NotImplementedError()
