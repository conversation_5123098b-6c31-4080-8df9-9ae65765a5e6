import logging
from typing import Any, Dict, List, Optional

from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.base_transformer import (
    TransformationError,
)
from finance_erp.domain.financial_transactions.services.reseller_transformers.transformer_factory import (
    ResellerTransformerFactory,
    TransformerFactoryError,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[ResellerTransformerFactory])
class ResellerEventGenerator:
    def __init__(self, transformer_factory: ResellerTransformerFactory):
        self.transformer_factory = transformer_factory

    def generate_buy_side_events(
        self, sell_events: List[FinancialEventEntity]
    ) -> List[FinancialEventEntity]:
        buy_side_events = []

        for sell_event in sell_events:
            try:
                if self._is_reseller_transaction(sell_event):
                    if self.transformer_factory.supports_event_type(
                        sell_event.entity_type
                    ):
                        buy_side_event = self._generate_buy_side_event(sell_event)
                        if buy_side_event:
                            buy_side_events.append(buy_side_event)
                            logger.info(
                                f"Generated buy-side event {buy_side_event.financial_event_id} "
                                f"from sell-side event {sell_event.financial_event_id}"
                            )
                    else:
                        logger.warning(
                            f"Reseller transaction detected but no transformer available "
                            f"for event type {sell_event.entity_type} in event {sell_event.financial_event_id}"
                        )

            except Exception as e:
                logger.error(
                    f"Failed to generate buy-side event for {sell_event.financial_event_id}: {str(e)}",
                    exc_info=True,
                )
                # Continue processing other events even if one fails
                continue

        logger.info(
            f"Generated {len(buy_side_events)} buy-side events from {len(sell_events)} sell-side events"
        )
        return buy_side_events

    @staticmethod
    def _is_reseller_transaction(event: FinancialEventEntity) -> bool:
        if not event.source_system_metadata:
            return False

        return event.source_system_metadata.get("is_reseller", False)

    def _generate_buy_side_event(
        self, sell_event: FinancialEventEntity
    ) -> Optional[FinancialEventEntity]:
        try:
            # Get the appropriate transformer for this event type
            transformer = self.transformer_factory.get_transformer(
                sell_event.entity_type
            )

            if not transformer:
                raise TransformerFactoryError(
                    f"No transformer available for event type {sell_event.entity_type}",
                    sell_event.entity_type,
                )

            # Transform the sell-side event to buy-side
            buy_side_event = transformer.transform_to_buy_side(sell_event)

            # Validate the generated event
            if self._validate_buy_side_event(buy_side_event, sell_event):
                return buy_side_event
            else:
                logger.error(
                    f"Generated buy-side event failed validation for {sell_event.financial_event_id}"
                )
                return None

        except (TransformationError, TransformerFactoryError) as e:
            logger.error(
                f"Transformation failed for event {sell_event.financial_event_id}: {str(e)}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error generating buy-side event for {sell_event.financial_event_id}: {str(e)}",
                exc_info=True,
            )
            return None

    @staticmethod
    def _validate_buy_side_event(
        buy_side_event: FinancialEventEntity,
        sell_event: FinancialEventEntity,
    ) -> bool:
        try:
            # Check required fields
            if not buy_side_event.financial_event_id:
                logger.error("Buy-side event missing financial_event_id")
                return False

            if not buy_side_event.entity_type:
                logger.error("Buy-side event missing entity_type")
                return False

            if not buy_side_event.event_data:
                logger.error("Buy-side event missing event_data")
                return False

            # Check that buy-side event ID is different from sell-side
            if buy_side_event.financial_event_id == sell_event.financial_event_id:
                logger.error("Buy-side event ID is the same as sell-side event ID")
                return False

            # Check that reseller metadata is set
            if not buy_side_event.source_system_metadata.get(
                "reseller_folio_transaction"
            ):
                logger.error("Buy-side event missing reseller_folio_transaction flag")
                return False

            # Check that original event ID is tracked
            if not buy_side_event.source_system_metadata.get("original_sell_event_id"):
                logger.error("Buy-side event missing original_sell_event_id")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating buy-side event: {str(e)}")
            return False

    def get_reseller_events_summary(
        self, events: List[FinancialEventEntity]
    ) -> Dict[str, Any]:
        total_events = len(events)
        reseller_events = [
            event for event in events if self._is_reseller_transaction(event)
        ]

        # Group by event type
        reseller_by_type = {}
        for event in reseller_events:
            event_type = event.entity_type
            if event_type not in reseller_by_type:
                reseller_by_type[event_type] = 0
            reseller_by_type[event_type] += 1

        # Check which types are supported
        supported_types = self.transformer_factory.get_supported_event_types()
        unsupported_reseller_events = [
            event
            for event in reseller_events
            if event.entity_type not in supported_types
        ]

        return {
            "total_events": total_events,
            "reseller_events_count": len(reseller_events),
            "reseller_events_by_type": reseller_by_type,
            "supported_event_types": supported_types,
            "unsupported_reseller_events_count": len(unsupported_reseller_events),
            "reseller_percentage": (len(reseller_events) / total_events * 100)
            if total_events > 0
            else 0,
        }
