import logging
from typing import List

from finance_erp.domain.back_office.entity.config_master import ConfigMaster
from finance_erp.domain.back_office.entity.ledger_controls import ErrorFileItem
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)
from finance_erp.domain.financial_transactions.services.event_processor.base_processor import (
    BaseProcessor,
)
from finance_erp.domain.financial_transactions.services.event_processor.registry import (
    TransactionProcessorRegistry,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[TransactionProcessorRegistry])
class TransactionGenerator:
    def __init__(self, transaction_processor_registry: TransactionProcessorRegistry):
        self.transaction_processor_registry = transaction_processor_registry

    def generate_transactions_from_events(
        self,
        financial_events: List[FinancialEventEntity],
        config_master: ConfigMaster,
    ) -> TransactionProcessingResult:

        result = TransactionProcessingResult.empty()

        for event in financial_events:
            try:
                tx_processor: BaseProcessor = self._get_transaction_processor(event)
                event_result = tx_processor.process(event, config_master)
                result.extend_with(event_result)

            except Exception as e:
                logger.exception(
                    "Failed to process financial event %s: %s",
                    event.financial_event_id,
                    str(e),
                )
                result.append_error(
                    ErrorFileItem(
                        financial_event_id=event.financial_event_id,
                        issue_type="PROCESSING_ERROR",
                        reason=str(e),
                        identifier=event.financial_event_id,
                        identifier_type="financial_event_id",
                        other_details=f"Failed to process financial event, unhandled exception {str(e)}",
                    )
                )

        return result

    def _get_transaction_processor(self, event) -> BaseProcessor:
        return self.transaction_processor_registry.get(event.entity_type)
