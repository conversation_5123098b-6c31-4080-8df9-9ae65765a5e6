import logging
from collections import defaultdict
from typing import Dict, List

from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
)
from finance_erp.domain.financial_transactions.entity.transaction_processing_result import (
    TransactionProcessingResult,
)

logger = logging.getLogger(__name__)


class PartialFailureTracker:
    @staticmethod
    def process_and_update_events(
        financial_events: List[FinancialEventEntity],
        processing_result: TransactionProcessingResult,
        processing_date_str: str,
    ) -> Dict[str, int]:

        event_statuses = PartialFailureTracker.analyze_processing_results(
            financial_events, processing_result
        )

        PartialFailureTracker.update_financial_events_status(
            financial_events, event_statuses, processing_result, processing_date_str
        )

        if processing_result.has_errors:
            PartialFailureTracker.log_detailed_failures(
                processing_result, event_statuses
            )

        return PartialFailureTracker.get_processing_summary(
            financial_events, event_statuses, processing_result
        )

    @staticmethod
    def analyze_processing_results(
        financial_events: List[FinancialEventEntity],
        processing_result: TransactionProcessingResult,
    ) -> Dict[str, str]:

        successful_events = set()
        for transaction in processing_result.transactions:
            if transaction.financial_event_id:
                successful_events.add(transaction.financial_event_id)

        failed_events = defaultdict(list)
        for error in processing_result.errors:
            if error.financial_event_id:
                failed_events[error.financial_event_id].append(error)

        event_statuses = {}

        for event in financial_events:
            event_id = event.financial_event_id
            has_success = event_id in successful_events
            has_failures = event_id in failed_events

            if has_success and has_failures:
                event_statuses[event_id] = "partial_failure"
                logger.warning(
                    "Financial event %s has partial failure: some transactions succeeded, %d failed",
                    event_id,
                    len(failed_events[event_id]),
                )
            elif has_success and not has_failures:
                event_statuses[event_id] = ProcessingStatus.PROCESSED.value
            elif has_failures and not has_success:
                event_statuses[event_id] = ProcessingStatus.FAILED.value
                logger.error(
                    "Financial event %s completely failed: %d errors",
                    event_id,
                    len(failed_events[event_id]),
                )
            else:
                event_statuses[event_id] = ProcessingStatus.FAILED.value
                logger.warning(
                    "Financial event %s has no transactions or errors - marking as failed",
                    event_id,
                )

        return event_statuses

    @staticmethod
    def update_financial_events_status(
        financial_events: List[FinancialEventEntity],
        event_statuses: Dict[str, str],
        processing_result: TransactionProcessingResult,
        processing_date_str: str,
    ) -> None:

        errors_by_event = defaultdict(list)
        for error in processing_result.errors:
            if error.financial_event_id:
                errors_by_event[error.financial_event_id].append(error)

        for event in financial_events:
            event_id = event.financial_event_id
            status = event_statuses.get(event_id, ProcessingStatus.FAILED.value)

            if status == ProcessingStatus.PROCESSED.value:
                event.mark_as_processed()

            elif status == ProcessingStatus.FAILED.value:
                failure_reasons = []
                for error in errors_by_event.get(event_id, []):
                    failure_reasons.append(f"{error.issue_type}: {error.reason}")

                failure_reason = (
                    "; ".join(failure_reasons)
                    if failure_reasons
                    else "No transactions generated"
                )
                event.mark_as_failed(failure_reason)

            elif status == "partial_failure":
                # Mark as partial failure with details
                event.mark_as_partial_failure(
                    errors_by_event.get(event_id, []),
                )

    @staticmethod
    def get_processing_summary(
        financial_events: List[FinancialEventEntity],
        event_statuses: Dict[str, str],
        processing_result: TransactionProcessingResult,
    ) -> Dict[str, int]:

        summary = {
            "total_events": len(financial_events),
            "successful_events": 0,
            "failed_events": 0,
            "partial_failure_events": 0,
            "total_transactions": len(processing_result.transactions),
            "total_errors": len(processing_result.errors),
        }

        for status in event_statuses.values():
            if status == ProcessingStatus.PROCESSED.value:
                summary["successful_events"] += 1
            elif status == ProcessingStatus.FAILED.value:
                summary["failed_events"] += 1
            elif status == ProcessingStatus.PARTIAL_FAILURE.value:
                summary["partial_failure_events"] += 1

        return summary

    @staticmethod
    def log_detailed_failures(
        processing_result: TransactionProcessingResult,
        event_statuses: Dict[str, str],
    ) -> None:

        errors_by_type = defaultdict(list)
        errors_by_event = defaultdict(list)

        for error in processing_result.errors:
            errors_by_type[error.issue_type].append(error)
            if error.financial_event_id:
                errors_by_event[error.financial_event_id].append(error)

        logger.info("Error summary by type:")
        for error_type, errors in errors_by_type.items():
            logger.info("  %s: %d errors", error_type, len(errors))

        partial_failures = [
            event_id
            for event_id, status in event_statuses.items()
            if status == "partial_failure"
        ]
        if partial_failures:
            logger.warning("Partial failure details:")
            for event_id in partial_failures:
                event_errors = errors_by_event.get(event_id, [])
                logger.warning(
                    "  Event %s: %d errors - %s",
                    event_id,
                    len(event_errors),
                    "; ".join(
                        [f"{e.issue_type}({e.identifier})" for e in event_errors[:3]]
                    ),
                )
