from datetime import date, datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel
from treebo_commons.utils import dateutils

from finance_erp.domain.financial_transactions.constants import (
    ProcessingStatus,
    SourceSystemStatus,
)


class SourceSystemEventEntity(BaseModel):

    event_id: str
    message_id: str
    source_system: str
    event_type: str
    hotel_id: Optional[str]
    system_scope: str = "hotel"
    entity_id: Optional[str]
    event_payload: Dict[str, Any]
    received_at: datetime
    processed_at: Optional[datetime] = None
    processing_status: ProcessingStatus = ProcessingStatus.RECEIVED
    failure_reason: Optional[str] = None
    retry_count: int = 0
    source_system_metadata: Optional[Dict[str, Any]] = None
    version_id: Optional[int] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    deleted: bool = False

    def mark_as_processed(self):
        self.processing_status = ProcessingStatus.PROCESSED
        self.processed_at = dateutils.current_datetime()

    def to_json(self) -> Dict[str, Any]:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True


class FinancialEventEntity(BaseModel):

    financial_event_id: str
    parent_event_id: str
    source_system: str
    entity_type: str
    entity_id: Union[str, int]
    document_number: Optional[str]
    hotel_id: str
    event_data: Dict[str, Any]
    source_system_status: SourceSystemStatus = SourceSystemStatus.CREATED
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    posting_date: Optional[date] = None
    processed_at: Optional[datetime] = None
    system_scope: str = "hotel"
    source_system_metadata: Optional[Dict[str, Any]] = None
    related_charge_id: Optional[Union[str, int]] = None
    related_charge_split_id: Optional[Union[str, int]] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def mark_as_processed(self):
        self.processing_status = ProcessingStatus.PROCESSED
        self.processed_at = dateutils.current_datetime()

    def mark_as_failed(self, reason: str):
        self.processing_status = ProcessingStatus.FAILED
        self.source_system_metadata = self.source_system_metadata or {}
        self.source_system_metadata["failure_reason"] = reason

    def mark_as_partial_failure(self, errors: List):
        self.processing_status = ProcessingStatus.PARTIAL_FAILURE
        self.processed_at = dateutils.current_datetime()
        self.source_system_metadata = self.source_system_metadata or {}

        # Store failure details
        failure_details = []
        for error in errors:
            failure_details.append(
                {
                    "issue_type": error.issue_type,
                    "identifier": error.identifier,
                    "reason": error.reason,
                    "txn_type": error.txn_type,
                    "accountable_entity": error.accountable_entity,
                }
            )

        self.source_system_metadata["partial_failure_details"] = failure_details
        self.source_system_metadata["partial_failure_count"] = len(errors)

    def to_json(self) -> Dict[str, Any]:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True

    @property
    def source_system_seller_id(self) -> Optional[str]:
        if not self.source_system_metadata:
            return None
        return self.source_system_metadata.get("pos_seller_id")
