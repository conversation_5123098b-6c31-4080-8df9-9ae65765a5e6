from collections import namedtuple
from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from finance_erp.domain.financial_transactions.constants import ProcessingStatus

TransactionGroupKey = namedtuple(
    "TransactionGroupKey",
    ["hotel_id", "posting_date", "gl_code"],
)


class TransactionEntity(BaseModel):

    transaction_id: str
    financial_event_id: str
    source_system: str
    source_entity_id: Optional[str]
    hotel_id: str
    txn_side: str
    posting_date: date
    triggering_source_type: Optional[str] = None
    document_number: Optional[str] = None

    # Transaction details (enriched from transaction master)
    txn_code: Optional[str] = None
    gl_code: Optional[str] = None
    erp_id: Optional[str] = None
    operational_unit: Optional[str] = None
    source: Optional[str] = None
    accountable_entity_type: Optional[str] = None

    # Financial amounts
    txn_amount: Decimal

    # Metadata
    particular: Optional[str] = None
    txn_group: Optional[str] = None
    is_mergeable: bool = True

    # Processing status
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    processed_at: Optional[datetime] = None
    aggregated_transaction_id: Optional[str] = None

    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def mark_as_processed(self, aggregated_transaction_id: str):
        self.processing_status = ProcessingStatus.PROCESSED
        self.processed_at = datetime.now()
        self.aggregated_transaction_id = aggregated_transaction_id

    def mark_as_failed(self, reason: str):
        self.processing_status = ProcessingStatus.FAILED
        self.metadata = self.metadata or {}
        self.metadata["failure_reason"] = reason

    def get_merge_key(self) -> TransactionGroupKey:
        return TransactionGroupKey(
            self.hotel_id,
            self.posting_date,
            self.gl_code,
        )

    def to_json(self) -> Dict[str, Any]:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True


class AggregatedTransactionEntity(BaseModel):

    aggregated_transaction_id: str
    hotel_id: str
    posting_date: date

    # Transaction details
    txn_code: Optional[str] = None
    gl_code: Optional[str] = None
    erp_id: Optional[str] = None
    operational_unit: Optional[str] = None
    source: Optional[str] = None

    # Aggregated amounts
    net_amount: Decimal = Decimal("0.00")
    txn_side: Optional[str] = None
    accountable_entity_type: Optional[str] = None

    # Aggregation metadata
    particular: Optional[str] = None
    txn_group: Optional[str] = None
    transaction_count: int = 1
    source_systems: Optional[List[str]] = None

    # Processing status
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    exported_at: Optional[datetime] = None
    export_batch_id: Optional[str] = None

    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def to_json(self) -> Dict[str, Any]:
        return self.model_dump()

    class Config:
        from_attributes = True
        validate_by_name = True
