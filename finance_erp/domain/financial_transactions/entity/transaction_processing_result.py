from dataclasses import field
from typing import Iterator, <PERSON>, Tuple

from pydantic import BaseModel

from finance_erp.domain.back_office.entity.ledger_controls import ErrorFileItem
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    TransactionEntity,
)


class TransactionProcessingResult(BaseModel):

    transactions: List[TransactionEntity] = field(default_factory=list)
    errors: List[ErrorFileItem] = field(default_factory=list)

    def __post_init__(self):
        if self.transactions is None:
            self.transactions = []
        if self.errors is None:
            self.errors = []

    @classmethod
    def empty(cls) -> "TransactionProcessingResult":
        return cls(transactions=[], errors=[])

    @classmethod
    def from_tuple(
        cls, result_tuple: Tuple[List[TransactionEntity], List[ErrorFileItem]]
    ) -> "TransactionProcessingResult":
        transactions, errors = result_tuple
        return cls(transactions=transactions or [], errors=errors or [])

    @classmethod
    def success(
        cls, transactions: List[TransactionEntity]
    ) -> "TransactionProcessingResult":
        return cls(transactions=transactions, errors=[])

    @classmethod
    def failure(cls, errors: List[ErrorFileItem]) -> "TransactionProcessingResult":
        return cls(transactions=[], errors=errors)

    def add_transaction(
        self, transaction: TransactionEntity
    ) -> "TransactionProcessingResult":
        return TransactionProcessingResult(
            transactions=self.transactions + [transaction], errors=self.errors.copy()
        )

    def add_error(self, error: ErrorFileItem) -> "TransactionProcessingResult":
        return TransactionProcessingResult(
            transactions=self.transactions.copy(), errors=self.errors + [error]
        )

    def extend(
        self, other: "TransactionProcessingResult"
    ) -> "TransactionProcessingResult":
        return TransactionProcessingResult(
            transactions=self.transactions + other.transactions,
            errors=self.errors + other.errors,
        )

    def extend_transactions(
        self, transactions: List[TransactionEntity]
    ) -> "TransactionProcessingResult":
        return TransactionProcessingResult(
            transactions=self.transactions + transactions, errors=self.errors.copy()
        )

    def extend_errors(
        self, errors: List[ErrorFileItem]
    ) -> "TransactionProcessingResult":
        return TransactionProcessingResult(
            transactions=self.transactions.copy(), errors=self.errors + errors
        )

    # Mutable operations for performance-critical scenarios
    def append_transaction(self, transaction: TransactionEntity) -> None:
        self.transactions.append(transaction)

    def append_error(self, error: ErrorFileItem) -> None:
        self.errors.append(error)

    def extend_with(self, other: "TransactionProcessingResult") -> None:
        self.transactions.extend(other.transactions)
        self.errors.extend(other.errors)

    def extend_transactions_with(self, transactions: List[TransactionEntity]) -> None:
        self.transactions.extend(transactions)

    def extend_errors_with(self, errors: List[ErrorFileItem]) -> None:
        self.errors.extend(errors)

    # Properties and utility methods
    @property
    def transaction_count(self) -> int:
        return len(self.transactions)

    @property
    def error_count(self) -> int:
        return len(self.errors)

    @property
    def has_transactions(self) -> bool:
        return len(self.transactions) > 0

    @property
    def has_errors(self) -> bool:
        return len(self.errors) > 0

    @property
    def is_empty(self) -> bool:
        return len(self.transactions) == 0 and len(self.errors) == 0

    @property
    def is_success(self) -> bool:
        return len(self.transactions) > 0 and len(self.errors) == 0

    @property
    def is_failure(self) -> bool:
        return len(self.transactions) == 0 and len(self.errors) > 0

    @property
    def is_partial_success(self) -> bool:
        return len(self.transactions) > 0 and len(self.errors) > 0

    def to_tuple(self) -> Tuple[List[TransactionEntity], List[ErrorFileItem]]:
        return self.transactions, self.errors

    def __iter__(self) -> Iterator[List]:
        yield self.transactions
        yield self.errors

    def __len__(self) -> int:
        return len(self.transactions) + len(self.errors)

    def __bool__(self) -> bool:
        return not self.is_empty

    def __str__(self) -> str:
        return f"TransactionProcessingResult(transactions={len(self.transactions)}, errors={len(self.errors)})"

    def __repr__(self) -> str:
        return f"TransactionProcessingResult(transactions={self.transactions}, errors={self.errors})"
