from finance_erp.domain.financial_transactions.constants import ProcessingStatus
from finance_erp.domain.financial_transactions.entity.transaction_entity import (
    AggregatedTransactionEntity,
    TransactionEntity,
)
from finance_erp.domain.financial_transactions.models import (
    AggregatedTransactionModel,
    TransactionModel,
)
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class TransactionAdaptor(BaseAdaptor):
    """Adaptor for Transaction"""

    def to_db_entity(self, domain_entity: TransactionEntity, **kwargs):
        return TransactionModel(
            transaction_id=domain_entity.transaction_id,
            financial_event_id=domain_entity.financial_event_id,
            source_system=domain_entity.source_system,
            source_entity_id=domain_entity.source_entity_id,
            hotel_id=domain_entity.hotel_id,
            posting_date=domain_entity.posting_date,
            triggering_source_type=domain_entity.triggering_source_type,
            document_number=domain_entity.document_number,
            txn_code=domain_entity.txn_code,
            gl_code=domain_entity.gl_code,
            erp_id=domain_entity.erp_id,
            operational_unit=domain_entity.operational_unit,
            accountable_entity_type=domain_entity.accountable_entity_type,
            source=domain_entity.source,
            txn_amount=domain_entity.txn_amount,
            txn_side=domain_entity.txn_side,
            particular=domain_entity.particular,
            txn_group=domain_entity.txn_group,
            is_mergeable=domain_entity.is_mergeable,
            processing_status=domain_entity.processing_status.value
            if isinstance(domain_entity.processing_status, ProcessingStatus)
            else domain_entity.processing_status,
            processed_at=domain_entity.processed_at,
            aggregated_transaction_id=domain_entity.aggregated_transaction_id,
            txn_metadata=domain_entity.metadata,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: TransactionModel):
        if not db_entity:
            return None
        return TransactionEntity(
            transaction_id=db_entity.transaction_id,
            financial_event_id=db_entity.financial_event_id,
            source_system=db_entity.source_system,
            source_entity_id=db_entity.source_entity_id,
            hotel_id=db_entity.hotel_id,
            posting_date=db_entity.posting_date,
            triggering_source_type=db_entity.triggering_source_type,
            document_number=db_entity.document_number,
            txn_code=db_entity.txn_code,
            gl_code=db_entity.gl_code,
            erp_id=db_entity.erp_id,
            operational_unit=db_entity.operational_unit,
            accountable_entity_type=db_entity.accountable_entity_type,
            source=db_entity.source,
            txn_amount=db_entity.txn_amount,
            particular=db_entity.particular,
            txn_group=db_entity.txn_group,
            is_mergeable=db_entity.is_mergeable,
            processing_status=ProcessingStatus(db_entity.processing_status)
            if db_entity.processing_status
            else ProcessingStatus.PENDING,
            processed_at=db_entity.processed_at,
            aggregated_transaction_id=db_entity.aggregated_transaction_id,
            metadata=db_entity.txn_metadata,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            txn_side=db_entity.txn_side,
        )


class AggregatedTransactionAdaptor(BaseAdaptor):
    """Adaptor for AggregatedTransaction"""

    def to_db_entity(self, domain_entity: AggregatedTransactionEntity, **kwargs):
        return AggregatedTransactionModel(
            aggregated_transaction_id=domain_entity.aggregated_transaction_id,
            hotel_id=domain_entity.hotel_id,
            posting_date=domain_entity.posting_date,
            txn_code=domain_entity.txn_code,
            gl_code=domain_entity.gl_code,
            erp_id=domain_entity.erp_id,
            operational_unit=domain_entity.operational_unit,
            source=domain_entity.source,
            net_amount=domain_entity.net_amount,
            particular=domain_entity.particular,
            txn_group=domain_entity.txn_group,
            txn_side=domain_entity.txn_side,
            accountable_entity_type=domain_entity.accountable_entity_type,
            transaction_count=domain_entity.transaction_count,
            source_systems=domain_entity.source_systems,
            processing_status=domain_entity.processing_status.value
            if isinstance(domain_entity.processing_status, ProcessingStatus)
            else domain_entity.processing_status,
            exported_at=domain_entity.exported_at,
            export_batch_id=domain_entity.export_batch_id,
            txn_metadata=domain_entity.metadata,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: AggregatedTransactionModel):
        if not db_entity:
            return None
        return AggregatedTransactionEntity(
            aggregated_transaction_id=db_entity.aggregated_transaction_id,
            hotel_id=db_entity.hotel_id,
            posting_date=db_entity.posting_date,
            txn_code=db_entity.txn_code,
            gl_code=db_entity.gl_code,
            erp_id=db_entity.erp_id,
            operational_unit=db_entity.operational_unit,
            source=db_entity.source,
            net_amount=db_entity.net_amount,
            particular=db_entity.particular,
            txn_group=db_entity.txn_group,
            txn_side=db_entity.txn_side,
            accountable_entity_type=db_entity.accountable_entity_type,
            transaction_count=db_entity.transaction_count,
            source_systems=db_entity.source_systems,
            processing_status=ProcessingStatus(db_entity.processing_status)
            if db_entity.processing_status
            else ProcessingStatus.PENDING,
            exported_at=db_entity.exported_at,
            export_batch_id=db_entity.export_batch_id,
            metadata=db_entity.txn_metadata,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
