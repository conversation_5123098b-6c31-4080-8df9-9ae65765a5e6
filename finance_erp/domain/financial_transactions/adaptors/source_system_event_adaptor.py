from finance_erp.domain.financial_transactions.constants import (
    ProcessingStatus,
    SourceSystemStatus,
)
from finance_erp.domain.financial_transactions.entity.source_system_event_entity import (
    FinancialEventEntity,
    SourceSystemEventEntity,
)
from finance_erp.domain.financial_transactions.models import (
    FinancialEventModel,
    SourceSystemEventModel,
)
from finance_erp.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)


class SourceSystemEventAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: SourceSystemEventEntity, **kwargs):
        return SourceSystemEventModel(
            event_id=domain_entity.event_id,
            message_id=domain_entity.message_id,
            source_system=domain_entity.source_system,
            event_type=domain_entity.event_type,
            hotel_id=domain_entity.hotel_id,
            system_scope=domain_entity.system_scope,
            entity_id=domain_entity.entity_id,
            event_payload=domain_entity.event_payload,
            received_at=domain_entity.received_at,
            processed_at=domain_entity.processed_at,
            processing_status=domain_entity.processing_status.value
            if isinstance(domain_entity.processing_status, ProcessingStatus)
            else domain_entity.processing_status,
            failure_reason=domain_entity.failure_reason,
            retry_count=domain_entity.retry_count,
            source_system_metadata=domain_entity.source_system_metadata,
            version_id=domain_entity.version_id,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
            deleted=domain_entity.deleted,
        )

    def to_domain_entity(self, db_entity: SourceSystemEventModel):
        if not db_entity:
            return None
        return SourceSystemEventEntity(
            event_id=db_entity.event_id,
            message_id=db_entity.message_id,
            source_system=db_entity.source_system,
            event_type=db_entity.event_type,
            hotel_id=db_entity.hotel_id,
            system_scope=db_entity.system_scope,
            entity_id=db_entity.entity_id,
            event_payload=db_entity.event_payload,
            received_at=db_entity.received_at,
            processed_at=db_entity.processed_at,
            processing_status=ProcessingStatus(db_entity.processing_status)
            if db_entity.processing_status
            else ProcessingStatus.RECEIVED,
            failure_reason=db_entity.failure_reason,
            retry_count=db_entity.retry_count,
            source_system_metadata=db_entity.source_system_metadata,
            version_id=db_entity.version_id,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            deleted=db_entity.deleted,
        )


class FinancialEventAdaptor(BaseAdaptor):
    """Adaptor for FinancialEvent"""

    def to_db_entity(self, domain_entity: FinancialEventEntity, **kwargs):
        return FinancialEventModel(
            financial_event_id=domain_entity.financial_event_id,
            parent_event_id=domain_entity.parent_event_id,
            source_system=domain_entity.source_system,
            entity_type=domain_entity.entity_type,
            entity_id=domain_entity.entity_id,
            document_number=domain_entity.document_number,
            hotel_id=domain_entity.hotel_id,
            posting_date=domain_entity.posting_date,
            event_data=domain_entity.event_data,
            processing_status=domain_entity.processing_status.value
            if isinstance(domain_entity.processing_status, ProcessingStatus)
            else domain_entity.processing_status,
            source_system_status=domain_entity.source_system_status.value
            if isinstance(domain_entity.source_system_status, SourceSystemStatus)
            else domain_entity.source_system_status,
            processed_at=domain_entity.processed_at,
            system_scope=domain_entity.system_scope,
            source_system_metadata=domain_entity.source_system_metadata,
            related_charge_id=domain_entity.related_charge_id,
            related_charge_split_id=domain_entity.related_charge_split_id,
            created_at=domain_entity.created_at,
            modified_at=domain_entity.modified_at,
        )

    def to_domain_entity(self, db_entity: FinancialEventModel):
        if not db_entity:
            return None
        return FinancialEventEntity(
            financial_event_id=db_entity.financial_event_id,
            parent_event_id=db_entity.parent_event_id,
            source_system=db_entity.source_system,
            entity_type=db_entity.entity_type,
            entity_id=db_entity.entity_id,
            document_number=db_entity.document_number,
            hotel_id=db_entity.hotel_id,
            posting_date=db_entity.posting_date,
            event_data=db_entity.event_data,
            processing_status=ProcessingStatus(db_entity.processing_status)
            if db_entity.processing_status
            else ProcessingStatus.PENDING,
            source_system_status=SourceSystemStatus(db_entity.source_system_status)
            if db_entity.source_system_status
            else SourceSystemStatus.CREATED,
            processed_at=db_entity.processed_at,
            system_scope=db_entity.system_scope,
            source_system_metadata=db_entity.source_system_metadata,
            related_charge_id=db_entity.related_charge_id,
            related_charge_split_id=db_entity.related_charge_split_id,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
