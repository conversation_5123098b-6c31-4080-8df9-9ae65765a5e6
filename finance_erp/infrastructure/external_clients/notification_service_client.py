from typing import List

from ths_common.exceptions import DownstreamSystemFailure
from ths_common.value_objects import EmailAttachment

from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


class NotificationSenderName:
    TREEBO_HOTELS = "Treebo Club"


class NotificationSenderEmail:
    TREEBO_HOTELS = "<EMAIL>"


@register_instance()
class NotificationServiceClient(BaseExternalClient):
    CONSUMER = "finance_erp"
    page_map = {
        "email": dict(
            type=BaseExternalClient.CallTypes.POST, url_regex="/v1/notification/email/"
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_notification_service_url()

    def email(
        self,
        body_html,
        subject,
        sender,
        recievers: List[str],
        reply_to=None,
        attachments: List[EmailAttachment] = None,
        sender_name=None,
        raise_on_failure=True,
        cc_list=None,
    ):
        request_dict = dict(
            body_html=body_html,
            subject=subject,
            sender=sender,
            consumer=self.CONSUMER,
            receivers={"to": recievers},
            attachments=[email_attchment.json() for email_attchment in attachments]
            if attachments is not None
            else [],
        )
        if cc_list:
            request_dict["receivers"]["cc"] = cc_list
        if reply_to:
            request_dict["reply_to"] = reply_to
        if sender_name:
            request_dict["sender_name"] = sender_name
        response = self.make_call("email", dict(data=request_dict))

        if not response.is_success():
            extra_payload = request_dict
            extra_payload.pop("body_html")
            if raise_on_failure:
                raise DownstreamSystemFailure(
                    message="Failed to send email.",
                    description="Failed to send email with code: {0}".format(
                        response.response_code
                    ),
                    extra_payload=extra_payload,
                )

        notification_id = response.json_response.get("data", {}).get("notification_id")
        return notification_id
