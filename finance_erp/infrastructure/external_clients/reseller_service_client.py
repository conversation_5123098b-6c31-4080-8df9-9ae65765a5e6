from finance_erp.common.exception import ResellerServiceException
from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class ResellerServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=4000)

    page_map = {
        "async_purchase_report_generation": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/hotelinvoice/get-reseller-invoice-reports",
        ),
        "fetch_hotel_invoice_finance_report": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/hotelinvoice/finance-reports",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_reseller_service_url()

    def trigger_async_purchase_report_generation(self, data):
        page_name = "async_purchase_report_generation"
        data = dict(job_name="push_invoice_reports_to_nav", job_data=data)
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise Exception(
                "Reseller API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def fetch_hotel_invoice_finance_report(self, unique_ref_ids):
        page_name = "fetch_hotel_invoice_finance_report"
        data = dict(unique_ref_ids=",".join(unique_ref_ids))
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise ResellerServiceException(
                "Reseller API (fetch_hotel_invoice_data) Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.data
