import json
import logging
import os

import requests
from flask import current_app as app
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from finance_erp.common.exception import NavisionDuplicateException
from finance_erp.common.globals import finance_erp_context
from finance_erp.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from finance_erp.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from finance_erp.infrastructure.external_clients.dtos.catalog_client.business_central_config_dto import (
    BusinessCentralConfig,
)
from object_registry import locate_instance, register_instance

logger = logging.getLogger(__name__)


@register_instance()
class BusinessCentralClient(BaseExternalClient):
    SUCCESS_MESSAGES = ["Requested", "Record Inserted", "Record Updated"]
    DUPLICATE_MESSAGES = "Duplicate"
    RESPONSE_MESSAGE_KEY = "value"

    page_map = {
        "data_push": dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/{resource_name}?company={company_name}",
        ),
    }
    DATA_PUSH_KEY = "data_push"

    def get_domain(self):
        return finance_erp_context.business_central_context.api_endpoint

    def is_success_response(self, json_response):
        return bool(
            isinstance(json_response, dict)
            and json_response.get(self.RESPONSE_MESSAGE_KEY) in self.SUCCESS_MESSAGES
        )

    def is_duplicate_push_response(self, json_response):
        return bool(
            isinstance(json_response, dict)
            and json_response.get(self.RESPONSE_MESSAGE_KEY) == self.DUPLICATE_MESSAGES
        )

    @staticmethod
    def _get_business_central_config():
        env = app.config.get("ENV")
        end_point_details = locate_instance(
            CatalogServiceClient
        ).get_business_central_endpoint_configs()
        if env not in ["production", "staging"]:
            auth_end_point = os.environ.get("MS_AUTH_END_POINT")
            client_id = os.environ.get("BC_CLIENT_ID")
            client_secret = os.environ.get("BC_CLIENT_SECRET")
            api_end_point = os.environ.get("BC_API_END_POINT")
        else:
            secret = AwsSecretManager.get_secret(
                tenant_id=get_current_tenant_id() or TenantClient.get_default_tenant(),
                secret_name="business-central-config",
            )
            auth_end_point = secret["microsoft_auth_end_point"]
            client_id = secret["client_id"]
            client_secret = secret["client_secret"]
            api_end_point = secret["api_end_point"]
        return BusinessCentralConfig(
            auth_end_point,
            end_point_details,
            client_id,
            client_secret,
            api_end_point,
        )

    def _populate_business_central_context_if_not_present(self):
        if not finance_erp_context.business_central_context:
            business_central_config = self._get_business_central_config()
            finance_erp_context.set_business_central_context(business_central_config)
            self._populate_business_central_access_token()

    @staticmethod
    def _populate_business_central_access_token():
        business_central_context = finance_erp_context.business_central_context
        auth_config = business_central_context.auth_config
        url = auth_config.auth_end_point
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }
        data = {
            "grant_type": auth_config.grant_type,
            "client_id": auth_config.client_id,
            "client_secret": auth_config.client_secret,
            "scope": auth_config.scope,
        }
        response = requests.post(url, headers=headers, data=data)
        response.raise_for_status()
        auth_config.set_access_token(response.json()["access_token"])

    def publish(self, data, report_name, is_retry=False):
        env = app.config.get("ENV")
        if env not in ["production", "staging"]:
            return True
        if not data:
            return True
        self._populate_business_central_context_if_not_present()
        end_point_details = (
            finance_erp_context.business_central_context.end_point_details
        )
        url_params = dict(
            resource_name=end_point_details.get(report_name),
            company_name=end_point_details.get("company_name"),
        )
        logger.info(
            "Data to be sent for %s to All e tech: %s",
            report_name,
            json.dumps(data),
        )
        response = self.make_call(
            self.DATA_PUSH_KEY,
            data,
            url_parameters=url_params,
        )
        logger.info(
            "Business Central response for %s with chunked data is %s",
            report_name,
            response.json_response,
        )

        if response.is_unauthorized() and not is_retry:
            self._populate_business_central_access_token()
            return self.publish(data, report_name, is_retry=True)

        if response.is_success() and self.is_duplicate_push_response(
            response.json_response
        ):
            raise NavisionDuplicateException()
        if not response.is_success() or not self.is_success_response(
            response.json_response
        ):
            raise Exception(
                "{0} Push Error. Status Code: {1}, Errors: {2}".format(
                    report_name, response.response_code, response.json_response
                )
            )

    @staticmethod
    def get_headers():
        return {
            "content-type": "application/json",
            "Authorization": f"Bearer {finance_erp_context.business_central_context.get_access_token()}",
        }
