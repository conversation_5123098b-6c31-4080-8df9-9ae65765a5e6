from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient


class QueueConfig(object):
    def __init__(self, queue_name, routing_keys):
        self.queue_name = queue_name
        self.routing_keys = routing_keys


class CRSConsumerConfig(object):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "crs-events"
        self.exchange_type = "topic"
        self.queues = [
            QueueConfig(
                queue_name="crs-events",
                routing_keys=["booking", "pos_order", "night_audit"],
            ),
        ]
        self.exclusive = False


class CompanyProfileConsumerConfig(object):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "company-profile-events"
        self.exchange_type = "topic"
        self.queues = [
            QueueConfig(queue_name="company-profile-event-queue", routing_keys=["#"])
        ]
        self.exclusive = False


class JobConsumerConfig(object):
    def __init__(
        self, can_consume_serial_jobs=False, tenant_id=TenantClient.get_default_tenant()
    ):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "finance-erp-job-exchange"
        self.exchange_type = "topic"
        self.queues = [
            QueueConfig(queue_name="parallel_job_queue", routing_keys=["parallel_job"])
        ]
        if can_consume_serial_jobs:
            self.queues.append(
                QueueConfig(queue_name="serial_job_queue", routing_keys=["serial_job"])
            )
        self.exclusive = False


class ARConsumerConfig(object):
    """
    AR Event Consumer Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        # self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.rabbitmq_url = "amqp://guest:guest@localhost:5672"
        self.exchange_name = "ar-event-publisher"
        self.exchange_type = "topic"
        self.queues = [
            QueueConfig(
                queue_name="ar-event-publisher-queue",
                routing_keys=[
                    "ar.events.credit.#",  # Credit events
                    "ar.events.debit.#",  # Debit events
                ],
            ),
        ]
        self.exclusive = False
