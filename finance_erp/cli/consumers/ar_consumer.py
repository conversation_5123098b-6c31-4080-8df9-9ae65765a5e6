import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from finance_erp.application.consumers.ar_consumer import ARConsumer
from finance_erp.application.consumers.routing import AREventRouter
from finance_erp.common.globals import consumer_context
from object_registry import inject


@click.command("start_ar_consumer")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@inject(
    ar_event_router=AREventRouter,
)
@with_appcontext
def start_ar_consumer(
    ar_event_router,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Starting AR Consumer for Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    consumer_context.tenant_id = tenant_id
    consumer = ARConsumer(
        ar_event_router,
        tenant_id,
    )
    consumer.start_consumer()
