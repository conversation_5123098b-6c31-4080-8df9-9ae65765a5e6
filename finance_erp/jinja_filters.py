from num2words import num2words

from core.common.utils.date import utc_to_ist


def convert_payment_amount_to_words(value):
    """
    Convert a decimal number into rupees-paise format
    """
    pre_decimal, _, post_decimal = str(value).partition(".")
    rupees = num2words(int(pre_decimal), lang="en_IN") if pre_decimal else "zero"
    paise = num2words(int(post_decimal), lang="en_IN") if post_decimal else "zero"
    return "Rupees {rupees} and {paise} paise only".format(rupees=rupees, paise=paise)


def dateformat_treebo_readable(date):
    """
    Datetime to MMM DD, YYYY. ie June 21, 2018
    """
    return date.strftime("%b %d, %Y")


def dateformat_treebo_standard(date):
    """
    Datetime to DD-MM-YYYY
    """
    return date.strftime("%d-%m-%Y")


def localize_to_ist(date):
    """
    Localizes to IST timezone
    """
    return utc_to_ist(date)
