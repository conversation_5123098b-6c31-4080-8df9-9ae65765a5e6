import logging

from flask import request
from ths_common.exceptions import ValidationException
from treebo_commons.request_tracing.context import get_current_hotel_id
from treebo_commons.utils.dateutils import date_to_ymd_str

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.request_parser import schema_wrapper_parser
from finance_erp.api.schema.request.back_office.ledgers import (
    LedgersFileGenerationRequestSchema,
)
from finance_erp.application.back_office.dtos.ledgers_dto import GenerateLedgersFileDto
from finance_erp.application.financial_transactions.transaction_engine import (
    TransactionEngine,
)
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.decorators import session_manager
from finance_erp.domain.policy.engine import RuleEngine
from finance_erp.domain.policy.facts.facts import Facts
from object_registry import inject

logger = logging.getLogger(__name__)


class FinancialTransactionGeneratorView(TreeboBaseAPI):
    @session_manager(commit=True)
    @schema_wrapper_parser(LedgersFileGenerationRequestSchema)
    @inject(
        job_scheduler=JobSchedulerService,
        txn_generator=TransactionEngine,
    )
    def post(
        self,
        job_scheduler: JobSchedulerService,
        txn_generator: TransactionEngine,
        parsed_request,
        **kwargs,
    ):
        hotel_id = get_current_hotel_id()
        self._do_privilege_check(hotel_id)

        request_dto = GenerateLedgersFileDto(**parsed_request, hotel_id=hotel_id)
        self._validate_erp_name(request_dto.erp_name, hotel_id)

        if self._async:
            job = job_scheduler.schedule_day_end_transaction_generation_job(
                processing_date=date_to_ymd_str(date_time=request_dto.date),
                hotel_id=request_dto.hotel_id,
            )
            return {"data": job.job_id}
        data = txn_generator.process(
            processing_date=request_dto.date,
            hotel_id=request_dto.hotel_id,
        )
        # TODO: Add response schema
        return {"data": True}

    @staticmethod
    def _do_privilege_check(hotel_id):
        if not hotel_id:
            raise ValidationException(description="X-Hotel-Id is missing")
        user = request.headers.get("X-USER-TYPE", None)
        if not user:
            raise ValidationException(description="X-User-Type is missing")
        RuleEngine.action_allowed(
            action="ledgers_data_rule",
            facts=Facts(user_type=user, hotel_id=hotel_id),
            fail_on_error=True,
        )
