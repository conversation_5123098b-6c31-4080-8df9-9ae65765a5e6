-- revision: '20250714120000_add_financial_transaction_tables'
-- down_revision: '202504051184511_add_purchase_types_in_purchase_table'

-- upgrade

-- Create source_system_events table
CREATE TABLE source_system_events (
    event_id VARCHAR NOT NULL PRIMARY KEY,
    message_id VARCHAR NOT NULL UNIQUE,
    source_system VARCHAR NOT NULL,
    event_type VARCHAR NOT NULL,
    hotel_id VARCHAR,
    system_scope VARCHAR NOT NULL DEFAULT 'hotel',
    entity_id VARCHAR,
    event_payload JSONB NOT NULL,
    received_at TIMESTAMP WITH TIME ZONE NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    processing_status VARCHAR NOT NULL DEFAULT 'received',
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    source_system_metadata JSONB,
    version_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted B<PERSON><PERSON><PERSON><PERSON> DEFAULT false
);

-- Create indexes for source_system_events
CREATE INDEX idx_source_events_system_type ON source_system_events (source_system, event_type);
CREATE INDEX idx_source_events_hotel_received ON source_system_events (hotel_id, received_at);
CREATE INDEX idx_source_events_status ON source_system_events (processing_status);
CREATE INDEX idx_source_events_scope ON source_system_events (system_scope);

-- Create financial_events table
CREATE TABLE financial_events (
    financial_event_id VARCHAR NOT NULL PRIMARY KEY,
    parent_event_id VARCHAR NOT NULL,
    source_system VARCHAR NOT NULL,
    entity_type VARCHAR NOT NULL,
    entity_id VARCHAR NOT NULL,
    document_number VARCHAR,
    hotel_id VARCHAR NOT NULL,
    posting_date DATE,
    event_data JSONB NOT NULL,
    processing_status VARCHAR NOT NULL DEFAULT 'pending',
    source_system_status VARCHAR,
    processed_at TIMESTAMP WITH TIME ZONE,
    system_scope VARCHAR NOT NULL DEFAULT 'hotel',
    source_system_metadata JSONB,
    related_charge_id VARCHAR,
    related_charge_split_id VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for financial_events
CREATE INDEX idx_financial_events_posting_date ON financial_events (hotel_id, posting_date);
CREATE INDEX idx_financial_events_entity_type ON financial_events (entity_type, processing_status);
CREATE INDEX idx_financial_events_parent ON financial_events (parent_event_id);
CREATE INDEX idx_financial_events_source_system ON financial_events (source_system, entity_type);
CREATE INDEX idx_financial_events_document_number ON financial_events (document_number);
CREATE INDEX idx_financial_events_scope ON financial_events (system_scope);

-- Create transactions table
CREATE TABLE transactions (
    transaction_id VARCHAR NOT NULL PRIMARY KEY,
    financial_event_id VARCHAR NOT NULL,
    source_system VARCHAR NOT NULL,
    source_entity_id VARCHAR,
    hotel_id VARCHAR NOT NULL,
    posting_date DATE NOT NULL,
    txn_code VARCHAR,
    gl_code VARCHAR,
    erp_id VARCHAR,
    triggering_source_type VARCHAR,
    document_number VARCHAR,
    operational_unit VARCHAR,
    source VARCHAR,
    accountable_entity_type VARCHAR,
    txn_amount NUMERIC(10, 2) NOT NULL,
    txn_side VARCHAR NOT NULL,
    particular TEXT,
    txn_group VARCHAR,
    is_mergeable BOOLEAN DEFAULT true,
    processing_status VARCHAR NOT NULL DEFAULT 'pending',
    processed_at TIMESTAMP WITH TIME ZONE,
    aggregated_transaction_id VARCHAR,
    txn_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted BOOLEAN DEFAULT false
);

-- Create indexes for transactions
CREATE INDEX idx_transactions_hotel_date ON transactions (hotel_id, posting_date);
CREATE INDEX idx_transactions_financial_event ON transactions (financial_event_id);
CREATE INDEX idx_transactions_status ON transactions (processing_status);
CREATE INDEX idx_transactions_mergeable ON transactions (is_mergeable, txn_code, gl_code);
CREATE INDEX idx_transactions_source ON transactions (source_system, source_entity_id);

-- Create aggregated_transactions table
CREATE TABLE aggregated_transactions (
    aggregated_transaction_id VARCHAR NOT NULL PRIMARY KEY,
    hotel_id VARCHAR NOT NULL,
    posting_date DATE,
    txn_code VARCHAR,
    gl_code VARCHAR,
    erp_id VARCHAR,
    operational_unit VARCHAR,
    source VARCHAR,
    net_amount NUMERIC(10, 2) NOT NULL,
    particular TEXT,
    txn_group VARCHAR,
    transaction_count INTEGER NOT NULL DEFAULT 1,
    txn_side VARCHAR,
    accountable_entity_type VARCHAR,
    source_systems JSONB,
    processing_status VARCHAR NOT NULL DEFAULT 'pending',
    exported_at TIMESTAMP WITH TIME ZONE,
    export_batch_id VARCHAR,
    txn_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    deleted BOOLEAN DEFAULT false
);

-- Create indexes for aggregated_transactions
CREATE INDEX idx_aggregated_transactions_hotel_date ON aggregated_transactions (hotel_id, posting_date);
CREATE INDEX idx_aggregated_transactions_status ON aggregated_transactions (processing_status);
CREATE INDEX idx_aggregated_transactions_export ON aggregated_transactions (export_batch_id);
CREATE INDEX idx_aggregated_transactions_codes ON aggregated_transactions (txn_code, gl_code);

-- downgrade

-- Drop tables in reverse order to handle dependencies
DROP TABLE IF EXISTS aggregated_transactions;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS financial_events;
DROP TABLE IF EXISTS source_system_events;
